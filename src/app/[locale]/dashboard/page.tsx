'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';
import { useDashboardTranslations } from '@/hooks/useTranslations';
import { useLocale } from '@/contexts/LocaleContext';
import { usePathname } from 'next/navigation';

import { SettingsModal } from '@/components/SettingsModal';
import { PageTransition } from '@/components/PageTransition';
import { useNavigationTransition } from '@/hooks/useNavigationTransition';
import { Sidebar } from '@/components/Sidebar';
import { Header } from '@/components/Header';
import AdminNotificationPanel from '@/components/AdminNotificationPanel';
import {
  Package,
  Truck,
  Users,
  DollarSign,
  ArrowUpRight,
  ArrowDownRight,
  Clock,
  MapPin,
  BarChart3,
  Activity,
  Target,
  Star,
  Download,
  ChevronDown,
  TrendingUp,
} from 'lucide-react';
import {
  Line<PERSON><PERSON>,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';

type Metric = {
  title: string;
  value: string;
  icon: React.ElementType;
  bgColor: string;
  textColor: string;
  trend: number;
  trendLabel: string;
};

type Order = {
  id: string;
  customer: string;
  avatar: string | null;
  status: 'Processing' | 'In Transit' | 'Delivered' | 'Cancelled';
  date: string;
  amount: string;
  address?: string;
  phone?: string;
  items?: Array<{ name: string; quantity: number; price: string }>;
};

type Delivery = {
  id: string;
  orderId?: string;
  address: string;
  driver: string;
  driverPhone?: string;
  eta: string;
  status: 'On Time' | 'Delayed' | 'Early';
  coordinates?: { lat: number; lng: number };
  estimatedDistance?: string;
  priority?: string;
};

type DashboardData = {
  metrics: {
    totalOrders: { current: number; previous: number; trend: number; trendLabel: string };
    activeDeliveries: { current: number; previous: number; trend: number; trendLabel: string };
    pendingOrders: { current: number; previous: number; trend: number; trendLabel: string };
    revenue: { current: number; previous: number; trend: number; trendLabel: string };
    deliverySuccess: { current: number; previous: number; trend: number; trendLabel: string };
    averageDeliveryTime: { current: number; previous: number; trend: number; trendLabel: string };
    customerSatisfaction: { current: number; previous: number; trend: number; trendLabel: string };
    activeDrivers: { current: number; previous: number; trend: number; trendLabel: string };
  };
  orders: Order[];
  deliveries: Delivery[];
  chartData: {
    orders: Array<{ date: string; value: number }>;
    revenue: Array<{ date: string; value: number }>;
    deliveries: Array<{ date: string; completed: number; failed: number }>;
  };
};

type TimePeriod = '7d' | '30d' | '3m';

// Chart colors for consistent theming
const CHART_COLORS = {
  primary: '#6366f1',
  secondary: '#8b5cf6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
  info: '#06b6d4',
  gray: '#6b7280',
};



const getStatusClass = (status: Order['status'] | Delivery['status']) => {
  switch (status) {
    case 'Processing':
      return 'bg-blue-100 text-blue-800';
    case 'In Transit':
      return 'bg-yellow-100 text-yellow-800';
    case 'Delivered':
      return 'bg-green-100 text-green-800';
    case 'Cancelled':
      return 'bg-red-100 text-red-800';
    case 'On Time':
      return 'bg-green-100 text-green-800';
    case 'Delayed':
      return 'bg-red-100 text-red-800';
    case 'Early':
      return 'bg-teal-100 text-teal-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// Helper function to get status translation for dashboard
const getStatusTranslation = (status: Order['status'] | Delivery['status'], t: (key: string) => string) => {
  const statusMap: Record<string, string> = {
    // Dashboard-specific statuses
    'Processing': t('dashboardStatus.processing'),
    'In Transit': t('dashboardStatus.inTransit'),
    'Delivered': t('dashboardStatus.delivered'),
    'Cancelled': t('dashboardStatus.cancelled'),
    'On Time': t('dashboardStatus.onTime'),
    'Delayed': t('dashboardStatus.delayed'),
    'Early': t('dashboardStatus.early'),
    // Order system statuses (from the actual orders API)
    'Pending': t('dashboardStatus.pending'),
    'Started': t('dashboardStatus.started'),
    'Moved to Supplier': t('dashboardStatus.movedToSupplier'),
    'Arrived at Supplier': t('dashboardStatus.arrivedAtSupplier'),
    'Moving to Customer': t('dashboardStatus.movingToCustomer'),
    'Arrived at Customer': t('dashboardStatus.arrivedAtCustomer')
  };
  return statusMap[status] || status;
};

// Helper function to get trend label translation
const getTrendLabelTranslation = (trendLabel: string, t: (key: string) => string) => {
  const trendLabelMap: Record<string, string> = {
    'vs last month': t('trendLabels.vsLastMonth'),
    'vs last week': t('trendLabels.vsLastWeek'),
    'vs yesterday': t('trendLabels.vsYesterday'),
    'hours vs last month': t('trendLabels.hoursVsLastMonth')
  };
  return trendLabelMap[trendLabel] || trendLabel;
};





const MetricCard: React.FC<Metric & { loading: boolean }> = ({ title, value, icon: Icon, bgColor, textColor, trend, trendLabel, loading }) => {
  const t = useDashboardTranslations();
  const TrendIcon = trend >= 0 ? ArrowUpRight : ArrowDownRight;
  const trendColor = trend >= 0 ? 'text-green-600' : 'text-red-600';

  return (
    <div className="bg-white p-6 rounded-2xl border border-gray-100 shadow-sm hover:shadow-xl hover:border-indigo-200 transition-all duration-300 flex items-start gap-5">
      <div className={`w-12 h-12 ${bgColor} ${textColor} rounded-xl flex items-center justify-center flex-shrink-0`}>
        <Icon className="w-6 h-6" />
      </div>
      <div className="flex-1">
        <p className="text-sm font-medium text-gray-500 mb-1">{title}</p>
        {loading ? (
          <div className="h-8 w-24 bg-gray-200 rounded animate-pulse mb-2" />
        ) : (
          <p className="text-3xl font-bold text-gray-900 mb-2">{value}</p>
        )}
        {loading ? (
          <div className="h-4 w-32 bg-gray-200 rounded animate-pulse" />
        ) : (
          <div className="flex items-center text-xs">
            <TrendIcon className={`w-4 h-4 mr-1 ${trendColor}`} />
            <span className={`font-semibold ${trendColor}`}>{Math.abs(trend)}%</span>
            <span className="text-gray-500 ml-1">{getTrendLabelTranslation(trendLabel, t)}</span>
          </div>
        )}
      </div>
    </div>
  );
};

const OrderRow: React.FC<Order & { loading: boolean }> = ({ id, customer, avatar, status, date, amount, loading }) => {
  const t = useDashboardTranslations();
  if (loading) {
    return (
      <tr className="animate-pulse">
        <td className="py-4 px-6"><div className="h-4 w-20 bg-gray-200 rounded"></div></td>
        <td className="py-4 px-6 flex items-center gap-3">
          <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
          <div className="h-4 w-28 bg-gray-200 rounded"></div>
        </td>
        <td className="py-4 px-6"><div className="h-6 w-24 bg-gray-200 rounded-full"></div></td>
        <td className="py-4 px-6"><div className="h-4 w-24 bg-gray-200 rounded"></div></td>
        <td className="py-4 px-6"><div className="h-4 w-16 bg-gray-200 rounded"></div></td>
      </tr>
    )
  }
  return (
    <tr className="border-b border-gray-100 hover:bg-gray-50">
      <td className="py-4 px-6 text-sm font-medium text-indigo-600">{id}</td>
      <td className="py-4 px-6 text-sm text-gray-900">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-white font-semibold">
            {avatar ? <Image src={avatar} alt={customer} width={32} height={32} className="w-full h-full rounded-full object-cover" /> : customer.charAt(0)}
          </div>
          {customer}
        </div>
      </td>
      <td className="py-4 px-6">
        <span className={`px-3 py-1 text-xs font-semibold rounded-full ${getStatusClass(status)}`}>
          {getStatusTranslation(status, t)}
        </span>
      </td>
      <td className="py-4 px-6 text-sm text-gray-500">{date}</td>
      <td className="py-4 px-6 text-sm font-semibold text-gray-900">{amount}</td>
    </tr>
  )
}

const DeliveryItem: React.FC<Delivery & { loading: boolean }> = ({ address, driver, eta, status, loading }) => {
  const t = useDashboardTranslations();
  if (loading) {
    return (
      <div className="animate-pulse flex items-center justify-between p-4 border-b border-gray-100">
        <div className="flex items-center gap-4">
          <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
          <div>
            <div className="h-4 w-40 bg-gray-200 rounded mb-1"></div>
            <div className="h-3 w-24 bg-gray-200 rounded"></div>
          </div>
        </div>
        <div className="h-6 w-20 bg-gray-200 rounded-full"></div>
      </div>
    )
  }
  return (
    <div className="flex items-center justify-between p-4 border-b border-gray-100 last:border-b-0 hover:bg-gray-50">
      <div className="flex items-center gap-4">
        <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
          <MapPin className="w-5 h-5 text-green-600" />
        </div>
        <div>
          <p className="text-sm font-medium text-gray-900 truncate max-w-xs">{address}</p>
          <p className="text-xs text-gray-500">Driver: {driver} | ETA: {eta}</p>
        </div>
      </div>
      <span className={`px-3 py-1 text-xs font-semibold rounded-full ${getStatusClass(status)}`}>
        {getStatusTranslation(status, t)}
      </span>
    </div>
  )
}

// Data fetching functions
const fetchDashboardData = async (period: TimePeriod = '7d'): Promise<DashboardData> => {
  try {
    const [metricsRes, ordersRes, deliveriesRes, chartDataRes] = await Promise.all([
      fetch(`/api/metrics?period=${period}`).catch(() => ({ ok: false, json: () => Promise.resolve({ success: false, data: null }) })),
      fetch('/api/orders?limit=5').catch(() => ({ ok: false, json: () => Promise.resolve({ success: false, data: [] }) })),
      fetch('/api/deliveries?limit=4').catch(() => ({ ok: false, json: () => Promise.resolve({ success: false, data: [] }) })),
      fetch(`/api/metrics?type=daily&period=${period}`).catch(() => ({ ok: false, json: () => Promise.resolve({ success: false, data: { orders: [], revenue: [], deliveries: [] } }) }))
    ]);

    const [metrics, orders, deliveries, chartData] = await Promise.all([
      metricsRes.json(),
      ordersRes.json(),
      deliveriesRes.json(),
      chartDataRes.json()
    ]);

    return {
      metrics: metrics.success && metrics.data ? metrics.data : {
        totalOrders: { current: 0, previous: 0, trend: 0, trendLabel: 'vs last period' },
        activeDeliveries: { current: 0, previous: 0, trend: 0, trendLabel: 'vs last period' },
        pendingOrders: { current: 0, previous: 0, trend: 0, trendLabel: 'vs last period' },
        revenue: { current: 0, previous: 0, trend: 0, trendLabel: 'vs last period' },
        deliverySuccess: { current: 0, previous: 0, trend: 0, trendLabel: 'vs last period' },
        averageDeliveryTime: { current: 0, previous: 0, trend: 0, trendLabel: 'vs last period' },
        customerSatisfaction: { current: 0, previous: 0, trend: 0, trendLabel: 'vs last period' },
        activeDrivers: { current: 0, previous: 0, trend: 0, trendLabel: 'vs last period' }
      },
      orders: orders.success ? orders.data || [] : [],
      deliveries: deliveries.success ? deliveries.data || [] : [],
      chartData: chartData.success ? chartData.data || { orders: [], revenue: [], deliveries: [] } : { orders: [], revenue: [], deliveries: [] }
    };
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    // Return empty state instead of throwing
    return {
      metrics: {
        totalOrders: { current: 0, previous: 0, trend: 0, trendLabel: 'vs last period' },
        activeDeliveries: { current: 0, previous: 0, trend: 0, trendLabel: 'vs last period' },
        pendingOrders: { current: 0, previous: 0, trend: 0, trendLabel: 'vs last period' },
        revenue: { current: 0, previous: 0, trend: 0, trendLabel: 'vs last period' },
        deliverySuccess: { current: 0, previous: 0, trend: 0, trendLabel: 'vs last period' },
        averageDeliveryTime: { current: 0, previous: 0, trend: 0, trendLabel: 'vs last period' },
        customerSatisfaction: { current: 0, previous: 0, trend: 0, trendLabel: 'vs last period' },
        activeDrivers: { current: 0, previous: 0, trend: 0, trendLabel: 'vs last period' }
      },
      orders: [],
      deliveries: [],
      chartData: { orders: [], revenue: [], deliveries: [] }
    };
  }
};





const Dashboard: React.FC = () => {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const t = useDashboardTranslations();
  const { locale } = useLocale();
  const pathname = usePathname();
  const [activeItem, setActiveItem] = useState('dashboard');
  const [loading, setLoading] = useState(true);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('7d');
  const [error, setError] = useState<string | null>(null);

  // Navigation transition hook - must be called before any early returns
  const { navigateWithTransition } = useNavigationTransition({ animationType: 'fade' });

  // Helper function to get display name from user
  const getDisplayName = () => {
    if (user?.displayName) {
      return user.displayName;
    }
    if (user?.email) {
      // Extract name from email (everything before @)
      const emailPrefix = user.email.split('@')[0];
      // Capitalize first letter and replace dots/underscores with spaces
      return emailPrefix
        .replace(/[._]/g, ' ')
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    }
    return 'User';
  };

  // Load dashboard data
  const loadDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await fetchDashboardData(selectedPeriod);
      setDashboardData(data);
    } catch (err) {
      setError('Failed to load dashboard data');
      console.error('Dashboard data loading error:', err);
    } finally {
      setLoading(false);
    }
  }, [selectedPeriod]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login');
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    if (user) {
      loadDashboardData();
    }
  }, [user, selectedPeriod, loadDashboardData]);

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (showUserMenu) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showUserMenu]);

  // Show loading spinner while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  // Don't render dashboard if user is not authenticated
  if (!user) {
    return null;
  }

  // Generate metrics data from API response
  const metricsData: Metric[] = dashboardData?.metrics ? [
    {
      title: t('totalOrders'),
      value: dashboardData.metrics.totalOrders.current.toLocaleString(),
      icon: Package,
      bgColor: 'bg-indigo-100',
      textColor: 'text-indigo-600',
      trend: dashboardData.metrics.totalOrders.trend,
      trendLabel: dashboardData.metrics.totalOrders.trendLabel,
    },
    {
      title: t('activeDeliveries'),
      value: dashboardData.metrics.activeDeliveries.current.toString(),
      icon: Truck,
      bgColor: 'bg-green-100',
      textColor: 'text-green-600',
      trend: dashboardData.metrics.activeDeliveries.trend,
      trendLabel: dashboardData.metrics.activeDeliveries.trendLabel,
    },
    {
      title: t('pendingOrders'),
      value: dashboardData.metrics.pendingOrders.current.toString(),
      icon: Clock,
      bgColor: 'bg-orange-100',
      textColor: 'text-orange-600',
      trend: dashboardData.metrics.pendingOrders.trend,
      trendLabel: dashboardData.metrics.pendingOrders.trendLabel,
    },
    {
      title: t('revenue'),
      value: `${dashboardData.metrics.revenue.current.toLocaleString()} EGP`,
      icon: DollarSign,
      bgColor: 'bg-purple-100',
      textColor: 'text-purple-600',
      trend: dashboardData.metrics.revenue.trend,
      trendLabel: dashboardData.metrics.revenue.trendLabel,
    },
  ] : [
    // Default empty state metrics
    {
      title: t('totalOrders'),
      value: '0',
      icon: Package,
      bgColor: 'bg-indigo-100',
      textColor: 'text-indigo-600',
      trend: 0,
      trendLabel: t('thisLastMonth'),
    },
    {
      title: t('activeDeliveries'),
      value: '0',
      icon: Truck,
      bgColor: 'bg-green-100',
      textColor: 'text-green-600',
      trend: 0,
      trendLabel: t('thisWeek'),
    },
    {
      title: t('pendingOrders'),
      value: '0',
      icon: Clock,
      bgColor: 'bg-orange-100',
      textColor: 'text-orange-600',
      trend: 0,
      trendLabel: t('thisWeek'),
    },
    {
      title: t('revenue'),
      value: '0 EGP',
      icon: DollarSign,
      bgColor: 'bg-purple-100',
      textColor: 'text-purple-600',
      trend: 0,
      trendLabel: t('thisLastMonth'),
    },
  ];

  // Additional metrics for enhanced analytics
  const additionalMetrics: Metric[] = dashboardData?.metrics ? [
    {
      title: t('deliverySuccessRate'),
      value: `${dashboardData.metrics.deliverySuccess.current}%`,
      icon: Target,
      bgColor: 'bg-emerald-100',
      textColor: 'text-emerald-600',
      trend: dashboardData.metrics.deliverySuccess.trend,
      trendLabel: dashboardData.metrics.deliverySuccess.trendLabel,
    },
    {
      title: t('avgDeliveryTime'),
      value: `${dashboardData.metrics.averageDeliveryTime.current}h`,
      icon: Clock,
      bgColor: 'bg-blue-100',
      textColor: 'text-blue-600',
      trend: dashboardData.metrics.averageDeliveryTime.trend,
      trendLabel: dashboardData.metrics.averageDeliveryTime.trendLabel,
    },
    {
      title: t('customerSatisfaction'),
      value: `${dashboardData.metrics.customerSatisfaction.current}/5`,
      icon: Star,
      bgColor: 'bg-yellow-100',
      textColor: 'text-yellow-600',
      trend: dashboardData.metrics.customerSatisfaction.trend,
      trendLabel: dashboardData.metrics.customerSatisfaction.trendLabel,
    },
    {
      title: t('activeDrivers'),
      value: dashboardData.metrics.activeDrivers.current.toString(),
      icon: Users,
      bgColor: 'bg-cyan-100',
      textColor: 'text-cyan-600',
      trend: dashboardData.metrics.activeDrivers.trend,
      trendLabel: dashboardData.metrics.activeDrivers.trendLabel,
    },
  ] : [
    // Default empty state additional metrics
    {
      title: t('deliverySuccessRate'),
      value: '0%',
      icon: Target,
      bgColor: 'bg-emerald-100',
      textColor: 'text-emerald-600',
      trend: 0,
      trendLabel: t('thisLastMonth'),
    },
    {
      title: t('avgDeliveryTime'),
      value: '0h',
      icon: Clock,
      bgColor: 'bg-blue-100',
      textColor: 'text-blue-600',
      trend: 0,
      trendLabel: t('thisLastMonth'),
    },
    {
      title: t('customerSatisfaction'),
      value: '0/5',
      icon: Star,
      bgColor: 'bg-yellow-100',
      textColor: 'text-yellow-600',
      trend: 0,
      trendLabel: t('thisLastMonth'),
    },
    {
      title: t('activeDrivers'),
      value: '0',
      icon: Users,
      bgColor: 'bg-cyan-100',
      textColor: 'text-cyan-600',
      trend: 0,
      trendLabel: t('thisWeek'),
    },
  ];



  // Navigation handler
  const handleNavigation = (path: string) => {
    navigateWithTransition(path);
  };

  return (
    <PageTransition transitionKey="dashboard" animationType="fade">
      <div className="h-screen bg-gray-50" data-page-content>
        {/* Sidebar */}
        <Sidebar
          activeItem={activeItem}
          setActiveItem={setActiveItem}
          isOpen={isSidebarOpen}
          setIsOpen={setIsSidebarOpen}
          onNavigate={handleNavigation}
          onOpenSettings={() => setShowSettings(true)}
        />

        {/* Main Content */}
        <div className="lg:ml-64 flex flex-col h-full overflow-hidden">
          {/* Header */}
          <Header
            showUserMenu={showUserMenu}
            setShowUserMenu={setShowUserMenu}
            setActiveItem={setActiveItem}
            setShowSettings={setShowSettings}
          />

          {/* Main Content Area */}
          <main className="flex-1 overflow-y-auto p-4 sm:p-6 lg:p-8 pt-56">
            <div className="space-y-8 mt-16">
              {/* Error Message */}
              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                  <p className="text-sm">{error}</p>
                  <button
                    onClick={loadDashboardData}
                    className="text-sm underline hover:no-underline mt-1"
                  >
                    Try again
                  </button>
                </div>
              )}

              {/* Welcome Banner */}
              <div className="bg-gradient-to-r from-indigo-50 to-purple-50 border-l-4 border-indigo-500 p-4 sm:p-6 rounded-xl mb-6 sm:mb-8 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 shadow-sm">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-1">{t('welcome')}, {getDisplayName()}!</h3>
                  <p className="text-gray-600 text-sm">{t('welcomeMessage')}</p>
                </div>
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <select
                      value={selectedPeriod}
                      onChange={(e) => setSelectedPeriod(e.target.value as TimePeriod)}
                      className="appearance-none bg-white border border-gray-300 text-gray-700 px-4 py-2 pr-8 rounded-lg text-sm font-medium hover:bg-gray-50 transition cursor-pointer"
                    >
                      <option value="7d">{t('last7Days')}</option>
                      <option value="30d">{t('last30Days')}</option>
                      <option value="3m">{t('last3Months')}</option>
                    </select>
                    <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                  </div>
                </div>
              </div>

              {/* Primary Metrics Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
                {metricsData.map((metric, index) => (
                  <MetricCard key={index} {...metric} loading={loading} />
                ))}
              </div>

              {/* Additional Analytics Metrics */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
                {additionalMetrics.map((metric, index) => (
                  <MetricCard key={`additional-${index}`} {...metric} loading={loading} />
                ))}
              </div>

              {/* Analytics Charts Section */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 mb-6 sm:mb-8">
                {/* Orders Trend Chart */}
                <div className="bg-white p-4 sm:p-6 rounded-2xl border border-gray-100 shadow-sm">
                  <div className="flex justify-between items-center mb-6">
                    <h3 className="text-lg font-semibold text-gray-900">{t('ordersTrend')}</h3>
                    <div className="flex items-center gap-2">
                      <BarChart3 className="w-4 h-4 text-gray-400" />
                      <button className="text-sm font-medium text-indigo-600 hover:text-indigo-800">
                        <Download className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  <div className="h-48 sm:h-64">
                    {loading ? (
                      <div className="h-full bg-gray-100 rounded animate-pulse" />
                    ) : (
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart data={dashboardData?.chartData.orders || []}>
                          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                          <XAxis
                            dataKey="date"
                            stroke="#6b7280"
                            fontSize={12}
                            tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                          />
                          <YAxis stroke="#6b7280" fontSize={12} />
                          <Tooltip
                            contentStyle={{
                              backgroundColor: 'white',
                              border: '1px solid #e5e7eb',
                              borderRadius: '8px',
                              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                            }}
                            labelFormatter={(value) => new Date(value).toLocaleDateString()}
                          />
                          <Line
                            type="monotone"
                            dataKey="value"
                            stroke={CHART_COLORS.primary}
                            strokeWidth={3}
                            dot={{ fill: CHART_COLORS.primary, strokeWidth: 2, r: 4 }}
                            activeDot={{ r: 6, stroke: CHART_COLORS.primary, strokeWidth: 2 }}
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    )}
                  </div>
                </div>

                {/* Revenue Chart */}
                <div className="bg-white p-6 rounded-2xl border border-gray-100 shadow-sm">
                  <div className="flex justify-between items-center mb-6">
                    <h3 className="text-lg font-semibold text-gray-900">{t('revenueTrend')}</h3>
                    <div className="flex items-center gap-2">
                      <TrendingUp className="w-4 h-4 text-gray-400" />
                      <button className="text-sm font-medium text-indigo-600 hover:text-indigo-800">
                        <Download className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  <div className="h-64">
                    {loading ? (
                      <div className="h-full bg-gray-100 rounded animate-pulse" />
                    ) : (
                      <ResponsiveContainer width="100%" height="100%">
                        <AreaChart data={dashboardData?.chartData.revenue || []}>
                          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                          <XAxis
                            dataKey="date"
                            stroke="#6b7280"
                            fontSize={12}
                            tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                          />
                          <YAxis
                            stroke="#6b7280"
                            fontSize={12}
                            tickFormatter={(value) => `$${(value / 1000).toFixed(1)}k`}
                          />
                          <Tooltip
                            contentStyle={{
                              backgroundColor: 'white',
                              border: '1px solid #e5e7eb',
                              borderRadius: '8px',
                              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                            }}
                            labelFormatter={(value) => new Date(value).toLocaleDateString()}
                            formatter={(value: number) => [`$${value.toLocaleString()}`, 'Revenue']}
                          />
                          <Area
                            type="monotone"
                            dataKey="value"
                            stroke={CHART_COLORS.success}
                            fill={CHART_COLORS.success}
                            fillOpacity={0.1}
                            strokeWidth={3}
                          />
                        </AreaChart>
                      </ResponsiveContainer>
                    )}
                  </div>
                </div>
              </div>

              {/* Delivery Performance Chart */}
              <div className="bg-white p-6 rounded-2xl border border-gray-100 shadow-sm mb-8">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">{t('deliveryPerformance')}</h3>
                  <div className="flex items-center gap-2">
                    <Activity className="w-4 h-4 text-gray-400" />
                    <button className="text-sm font-medium text-indigo-600 hover:text-indigo-800">
                      <Download className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                <div className="h-64">
                  {loading ? (
                    <div className="h-full bg-gray-100 rounded animate-pulse" />
                  ) : (
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={dashboardData?.chartData.deliveries || []}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis
                          dataKey="date"
                          stroke="#6b7280"
                          fontSize={12}
                          tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                        />
                        <YAxis stroke="#6b7280" fontSize={12} />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: 'white',
                            border: '1px solid #e5e7eb',
                            borderRadius: '8px',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                          }}
                          labelFormatter={(value) => new Date(value).toLocaleDateString()}
                        />
                        <Legend />
                        <Bar dataKey="completed" fill={CHART_COLORS.success} name={t('completed')} radius={[4, 4, 0, 0]} />
                        <Bar dataKey="failed" fill={CHART_COLORS.danger} name={t('failed')} radius={[4, 4, 0, 0]} />
                      </BarChart>
                    </ResponsiveContainer>
                  )}
                </div>
              </div>

              {/* Recent Activity */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
                {/* Recent Orders Table */}
                <div className="lg:col-span-2 bg-white p-4 sm:p-6 rounded-2xl border border-gray-100 shadow-sm">
                  <div className="flex justify-between items-center mb-6">
                    <h3 className="text-lg font-semibold text-gray-900">{t('recentOrders')}</h3>
                    <button className="text-sm font-medium text-indigo-600 hover:text-indigo-800">{t('viewAll')}</button>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="min-w-full bg-white">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('orderId')}</th>
                          <th className="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('customer')}</th>
                          <th className="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('status')}</th>
                          <th className="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('date')}</th>
                          <th className="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('amount')}</th>
                        </tr>
                      </thead>
                      <tbody>
                        {loading ? (
                          // Show loading rows
                          Array.from({ length: 3 }).map((_, index) => (
                            <OrderRow key={`loading-${index}`} id="" customer="" avatar={null} status="Processing" date="" amount="" loading={true} />
                          ))
                        ) : (dashboardData?.orders || []).length > 0 ? (
                          (dashboardData?.orders || []).map((order) => (
                            <OrderRow key={order.id} {...order} loading={false} />
                          ))
                        ) : (
                          <tr>
                            <td colSpan={5} className="py-8 px-6 text-center text-gray-500">
                              <div className="flex flex-col items-center">
                                <Package className="w-12 h-12 text-gray-300 mb-3" />
                                <p className="text-sm font-medium">{t('noOrdersYet')}</p>
                                <p className="text-xs text-gray-400 mt-1">{t('ordersWillAppear')}</p>
                              </div>
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* Active Deliveries List */}
                <div className="bg-white p-6 rounded-2xl border border-gray-100 shadow-sm">
                  <div className="flex justify-between items-center mb-6">
                    <h3 className="text-lg font-semibold text-gray-900">{t('activeDeliveriesTitle')}</h3>
                    <button className="text-sm font-medium text-indigo-600 hover:text-indigo-800">{t('viewMap')}</button>
                  </div>
                  <div className="space-y-2">
                    {loading ? (
                      // Show loading items
                      Array.from({ length: 3 }).map((_, index) => (
                        <DeliveryItem key={`loading-${index}`} id="" address="" driver="" eta="" status="On Time" loading={true} />
                      ))
                    ) : (dashboardData?.deliveries || []).length > 0 ? (
                      (dashboardData?.deliveries || []).map((delivery) => (
                        <DeliveryItem key={delivery.id} {...delivery} loading={false} />
                      ))
                    ) : (
                      <div className="py-8 text-center text-gray-500">
                        <div className="flex flex-col items-center">
                          <Truck className="w-12 h-12 text-gray-300 mb-3" />
                          <p className="text-sm font-medium">{t('noActiveDeliveries')}</p>
                          <p className="text-xs text-gray-400 mt-1">{t('activeDeliveriesWillAppear')}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Admin Notification Panel - Only show for admin users */}
              {user?.email === '<EMAIL>' && (
                <div className="mt-8">
                  <AdminNotificationPanel />
                </div>
              )}

            </div>
          </main>

        </div>

        {/* Settings Modal */}
        <SettingsModal
          isOpen={showSettings}
          onClose={() => setShowSettings(false)}
        />
      </div>
    </PageTransition>
  );
};

export default Dashboard;