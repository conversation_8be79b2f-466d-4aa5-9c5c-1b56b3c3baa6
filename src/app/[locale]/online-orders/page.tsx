'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigationTranslations, useCommonTranslations, useTranslations } from '@/hooks/useTranslations';
import { ResponsiveTable, ResponsiveTableRow, ResponsiveTableCell } from '@/components/ResponsiveTable';
import {
  Globe,
  Package,
  Truck,
  Users,
  Search,
  X,
  MapPin,
  Clock,
  CheckCircle,
  XCircle,
  Edit,
  Phone,
  ArrowRight,
  Calendar,
  ChevronDown,
  ChevronUp,
  ClipboardList,
  Play,
  Building2,
  Car,
  Home,
  Gift,
  DollarSign,
  Plus,
  ShoppingCart,
  Monitor,
  Smartphone,
  Mail,
  CreditCard,
  Tag,
  FileText,
  Copy,
  ExternalLink,
  User,
  Check,
  Ban,
  AlertTriangle,
  Download
} from 'lucide-react';

import { SettingsModal } from '@/components/SettingsModal';
import { PageTransition } from '@/components/PageTransition';
import { CompactModal } from '@/components/CompactModal';
import { useNavigationTransition } from '@/hooks/useNavigationTransition';
import { Sidebar } from '@/components/Sidebar';
import { Header } from '@/components/Header';
import { ShopifyOrdersService, OnlineOrder } from '@/services/shopify/ordersService';
import { useOrders, ApprovedOrder } from '@/contexts/OrdersContext';

// Types
type OnlineOrderStatus =
  | 'Pending'
  | 'Approved'
  | 'Processing'
  | 'Confirmed'
  | 'Shipped'
  | 'Delivered'
  | 'Cancelled';

// Main Online Orders Page Component
const OnlineOrdersPage: React.FC = () => {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const t = useNavigationTranslations();
  const common = useCommonTranslations();
  const onlineOrdersT = useTranslations('onlineOrders');
  const { addApprovedOrder } = useOrders();

  const [activeItem, setActiveItem] = useState('onlineOrders');
  const [orders, setOrders] = useState<OnlineOrder[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<OnlineOrder[]>([]);
  const [selectedOrder, setSelectedOrder] = useState<OnlineOrder | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<OnlineOrderStatus | 'All'>('All');
  const [platformFilter, setPlatformFilter] = useState<OnlineOrder['platform'] | 'All'>('All');
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [statsLoading, setStatsLoading] = useState(true);
  const [showCancellationModal, setShowCancellationModal] = useState(false);
  const [orderToCancel, setOrderToCancel] = useState<OnlineOrder | null>(null);
  const [isSyncing, setIsSyncing] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Navigation transition hook
  const { navigateWithTransition } = useNavigationTransition({ animationType: 'fade' });

  // Handle navigation
  const handleNavigation = (path: string) => {
    navigateWithTransition(path);
  };

  // Handle sync orders
  const handleSyncOrders = async () => {
    setIsSyncing(true);
    try {
      // Simulate API call to sync orders from all platforms
      await new Promise(resolve => setTimeout(resolve, 2000));

      // In a real implementation, this would:
      // 1. Fetch latest orders from Shopify API
      // 2. Fetch orders from other platforms (WooCommerce, etc.)
      // 3. Update the orders state with new data
      // 4. Show success notification

      // For now, we'll just show a success message
      alert('Orders synced successfully! New orders have been fetched from all platforms.');

      // Refresh the page data
      window.location.reload();
    } catch (error) {
      console.error('Error syncing orders:', error);
      alert('Failed to sync orders. Please try again.');
    } finally {
      setIsSyncing(false);
    }
  };

  // Handle export data
  const handleExportData = async () => {
    setIsExporting(true);
    try {
      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Create CSV content
      const csvHeaders = ['Order ID', 'Customer', 'Platform', 'Total', 'Status', 'Date'];
      const csvData = orders.map(order => [
        order.id,
        order.customer,
        order.platform,
        order.total,
        order.status,
        order.date
      ]);

      const csvContent = [
        csvHeaders.join(','),
        ...csvData.map(row => row.join(','))
      ].join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `online-orders-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      alert('Orders data exported successfully!');
    } catch (error) {
      console.error('Error exporting data:', error);
      alert('Failed to export data. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  // Authentication check
  useEffect(() => {
    if (!authLoading && !user) {
      const currentLocale = window.location.pathname.split('/')[1] || 'en';
      router.push(`/${currentLocale}/login`);
    }

    // Simulate loading stats
    const timer = setTimeout(() => {
      setStatsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [user, authLoading, router]);

  // Fetch Shopify pending orders on mount
  useEffect(() => {
    fetch('/api/shopify/orders?status=pending&limit=50')
      .then(async (res) => {
        if (!res.ok) throw new Error('Failed to fetch orders');
        const data = await res.json();
        setOrders(data.data || []);
        setFilteredOrders(data.data || []);
      })
      .catch(error => {
        // Optionally handle error, e.g., show a toast
        setOrders([]);
        setFilteredOrders([]);
      });
  }, []);

  // Filter orders based on search term, status, and platform
  useEffect(() => {
    let filtered = orders;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'All') {
      filtered = filtered.filter(order => order.status === statusFilter);
    }

    // Filter by platform
    if (platformFilter !== 'All') {
      filtered = filtered.filter(order => order.platform === platformFilter);
    }

    setFilteredOrders(filtered);
  }, [orders, searchTerm, statusFilter, platformFilter]);

  // Column definitions for online orders table
  const onlineOrderColumns = [
    { key: 'orderId', label: 'Order ID', className: 'min-w-[120px]' },
    { key: 'customer', label: 'Customer', className: 'min-w-[180px]' },
    { key: 'platform', label: 'Platform', className: 'min-w-[120px]' },
    { key: 'total', label: 'Total', className: 'min-w-[100px]' },
    { key: 'status', label: 'Status', className: 'min-w-[120px]' },
    { key: 'orderDate', label: 'Order Date', className: 'min-w-[120px]' },
    { key: 'actions', label: 'Actions', className: 'min-w-[100px]' }
  ];

  // Update renderOnlineOrderRow and renderOnlineOrderMobileCard to open details modal
  const handleOrderClick = (order: OnlineOrder) => setSelectedOrder(order);

  // Handle order approval
  const handleApproveOrder = (orderId: string) => {
    try {
      // Find the order to approve
      const orderToApprove = orders.find(order => order.id === orderId);
      if (!orderToApprove) {
        alert('Order not found');
        return;
      }

      // Update local state to "Approved"
      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId
            ? { ...order, status: 'Approved' }
            : order
        )
      );

      setFilteredOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId
            ? { ...order, status: 'Approved' }
            : order
        )
      );

      // Convert to ApprovedOrder and add to purchasing context
      const approvedOrder: ApprovedOrder = {
        ...orderToApprove,
        status: 'Pending', // Reset to Pending for purchasing workflow
        approvedDate: new Date().toISOString(),
        originalOrderId: orderToApprove.id
      };

      // Add to purchasing context
      addApprovedOrder(approvedOrder);

      console.log(`Order ${orderId} approved and moved to purchasing`);

    } catch (error) {
      console.error('Error approving order:', error);
      alert('Failed to approve order. Please try again.');
    }
  };

  // Handle order denial (open cancellation modal)
  const handleDenyOrder = (order: OnlineOrder) => {
    setOrderToCancel(order);
    setShowCancellationModal(true);
  };

  // Handle order cancellation confirmation
  const handleCancelOrder = async (orderId: string, reason: string, notes?: string) => {
    try {
      // Update local state immediately
      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId
            ? { ...order, status: 'Cancelled' }
            : order
        )
      );

      setFilteredOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId
            ? { ...order, status: 'Cancelled' }
            : order
        )
      );

      // Call API to cancel order
      const response = await fetch(`/api/shopify/orders/${orderId}/cancel`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ reason, notes })
      });

      if (!response.ok) {
        throw new Error('Failed to cancel order');
      }

      console.log(`Order ${orderId} cancelled successfully`);

    } catch (error) {
      console.error('Error cancelling order:', error);
      // Revert changes on error
      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId
            ? { ...order, status: 'Pending' }
            : order
        )
      );

      setFilteredOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId
            ? { ...order, status: 'Pending' }
            : order
        )
      );

      alert('Failed to cancel order. Please try again.');
    }
  };

  // Render online order table row
  const renderOnlineOrderRow = (order: OnlineOrder) => (
    <ResponsiveTableRow key={order.id} onClick={() => handleOrderClick(order)} className="cursor-pointer">
      <ResponsiveTableCell className="font-medium text-indigo-600">
        {order.id}
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-full flex items-center justify-center">
            <ShoppingCart className="w-5 h-5 text-indigo-600" />
          </div>
          <div>
            <div className="text-sm font-medium text-gray-900">{order.customer}</div>
            <div className="text-sm text-gray-500">{order.email}</div>
          </div>
        </div>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <div className="flex items-center gap-2">
          {getPlatformIcon(order.platform)}
          <span className="text-sm text-gray-900">{order.platform}</span>
        </div>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <div className="text-sm font-medium text-gray-900">{order.total} {order.currency}</div>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(order.status)}`}>
          {order.status}
        </span>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <div className="text-sm text-gray-900">{order.orderDate}</div>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <div className="flex items-center gap-2">
          {order.status === 'Pending' ? (
            <>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleApproveOrder(order.id);
                }}
                className="group relative bg-gradient-to-r from-emerald-500 to-green-500 text-white p-2.5 rounded-xl hover:from-emerald-600 hover:to-green-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 active:translate-y-0"
                title="Approve Order"
              >
                <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 rounded-xl transition-opacity duration-200"></div>
                <Check className="w-4 h-4 relative z-10" />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleDenyOrder(order);
                }}
                className="group relative bg-gradient-to-r from-red-500 to-rose-500 text-white p-2.5 rounded-xl hover:from-red-600 hover:to-rose-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 active:translate-y-0"
                title="Deny Order"
              >
                <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 rounded-xl transition-opacity duration-200"></div>
                <Ban className="w-4 h-4 relative z-10" />
              </button>
            </>
          ) : (
            <span className="text-xs text-gray-500 px-2 py-1 bg-gray-100 rounded-full">
              {order.status}
            </span>
          )}
        </div>
      </ResponsiveTableCell>
    </ResponsiveTableRow>
  );

  // Render online order mobile card
  const renderOnlineOrderMobileCard = (order: OnlineOrder) => (
    <div key={order.id} className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow" onClick={() => handleOrderClick(order)}>
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-full flex items-center justify-center">
            <ShoppingCart className="w-5 h-5 text-indigo-600" />
          </div>
          <div>
            <h3 className="text-sm font-semibold text-gray-900">{order.id}</h3>
            <p className="text-xs text-gray-500">{order.customer}</p>
          </div>
        </div>
        <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(order.status)}`}>
          {order.status}
        </span>
      </div>

      <div className="space-y-2 text-xs text-gray-600">
        <div className="flex items-center gap-1">
          {getPlatformIcon(order.platform)}
          <span>{order.platform}</span>
        </div>
        <div className="flex items-center gap-1">
          <DollarSign className="w-3 h-3" />
          <span className="font-medium">{order.total} {order.currency}</span>
        </div>
        <div className="flex items-center gap-1">
          <Calendar className="w-3 h-3" />
          <span>{order.orderDate}</span>
        </div>
        <div className="flex items-center gap-1">
          <Phone className="w-3 h-3" />
          <span>{order.phone}</span>
        </div>
      </div>

      <div className="mt-3 pt-3 border-t border-gray-100">
        <p className="text-xs text-gray-500 mb-1">Items ({order.items.length})</p>
        <div className="space-y-1">
          {order.items.slice(0, 2).map((item, index) => (
            <div key={index} className="flex justify-between text-xs">
              <span>{item.name} × {item.quantity}</span>
              <span className="font-medium">{item.price} {order.currency}</span>
            </div>
          ))}
          {order.items.length > 2 && (
            <p className="text-xs text-gray-400">+{order.items.length - 2} more items</p>
          )}
        </div>
      </div>

      <div className="flex items-center justify-end gap-2 mt-3 pt-3 border-t border-gray-100">
        {order.status === 'Pending' ? (
          <>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleApproveOrder(order.id);
              }}
              className="group relative bg-gradient-to-r from-emerald-500 to-green-500 text-white p-2.5 rounded-xl hover:from-emerald-600 hover:to-green-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 active:translate-y-0"
              title="Approve Order"
            >
              <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 rounded-xl transition-opacity duration-200"></div>
              <Check className="w-4 h-4 relative z-10" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleDenyOrder(order);
              }}
              className="group relative bg-gradient-to-r from-red-500 to-rose-500 text-white p-2.5 rounded-xl hover:from-red-600 hover:to-rose-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 active:translate-y-0"
              title="Deny Order"
            >
              <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 rounded-xl transition-opacity duration-200"></div>
              <Ban className="w-4 h-4 relative z-10" />
            </button>
          </>
        ) : (
          <span className="text-xs text-gray-500 px-2 py-1 bg-gray-100 rounded-full">
            {order.status}
          </span>
        )}
      </div>
    </div>
  );

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <PageTransition transitionKey="online-orders" animationType="fade">
      <div className="h-screen bg-gray-50 text-gray-900" data-page-content>
        <Sidebar
          activeItem={activeItem}
          setActiveItem={setActiveItem}
          isOpen={isSidebarOpen}
          setIsOpen={setIsSidebarOpen}
          onNavigate={handleNavigation}
          onOpenSettings={() => setShowSettings(true)}
        />
        <main className="lg:ml-64 flex flex-col h-full overflow-hidden">
          <Header
            showUserMenu={showUserMenu}
            setShowUserMenu={setShowUserMenu}
            setActiveItem={setActiveItem}
            setShowSettings={setShowSettings}
          />
          <div className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-8 pt-25">
            {/* Creative Add New Online Order Section */}
            <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 rounded-3xl p-8 mb-8 text-white relative overflow-hidden">
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-4 left-4 w-20 h-20 border-2 border-white rounded-full"></div>
                <div className="absolute top-12 right-8 w-16 h-16 border-2 border-white rounded-full"></div>
                <div className="absolute bottom-8 left-12 w-12 h-12 border-2 border-white rounded-full"></div>
                <div className="absolute bottom-4 right-16 w-8 h-8 border-2 border-white rounded-full"></div>
              </div>

              <div className="relative z-10 flex flex-col md:flex-row items-center justify-between">
                <div className="mb-6 md:mb-0">
                  <h2 className="text-3xl font-bold mb-2">Manage Online Orders</h2>
                  <p className="text-lg opacity-90 mb-4">Track and manage orders from all digital platforms</p>
                  <div className="flex items-center gap-4 text-sm opacity-80">
                    <div className="flex items-center gap-2">
                      <Monitor className="w-4 h-4" />
                      <span>Website Orders</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Smartphone className="w-4 h-4" />
                      <span>Mobile App</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Globe className="w-4 h-4" />
                      <span>Social Media</span>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4">
                  <button
                    onClick={handleSyncOrders}
                    disabled={isSyncing}
                    className="bg-white text-indigo-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-50 transition duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 flex items-center gap-3 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  >
                    {isSyncing ? (
                      <div className="w-6 h-6 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <Plus className="w-6 h-6" />
                    )}
                    {isSyncing ? 'Syncing...' : 'Sync Orders'}
                  </button>
                  <button
                    onClick={handleExportData}
                    disabled={isExporting}
                    className="bg-white/20 backdrop-blur-sm border-2 border-white text-white px-8 py-4 rounded-2xl font-bold text-lg hover:bg-white/30 transition duration-300 flex items-center gap-3 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isExporting ? (
                      <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <Download className="w-6 h-6" />
                    )}
                    {isExporting ? 'Exporting...' : 'Export Data'}
                  </button>
                </div>
              </div>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Online Orders</p>
                    <p className="text-3xl font-bold text-gray-900">{orders.length}</p>
                  </div>
                  <Globe className="w-8 h-8 text-indigo-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pending Orders</p>
                    <p className="text-3xl font-bold text-blue-600">
                      {orders.filter(order => order.status === 'Pending').length}
                    </p>
                  </div>
                  <Clock className="w-8 h-8 text-blue-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Approved Orders</p>
                    <p className="text-3xl font-bold text-green-600">
                      {orders.filter(order => order.status === 'Approved').length}
                    </p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Cancelled Orders</p>
                    <p className="text-3xl font-bold text-red-600">
                      {orders.filter(order => order.status === 'Cancelled').length}
                    </p>
                  </div>
                  <XCircle className="w-8 h-8 text-red-600" />
                </div>
              </div>
            </div>

            {/* Online Orders Table */}
            <div className="bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden">
              {/* Header with integrated search and filters */}
              <div className="p-4 sm:p-6 border-b border-gray-100">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                  <div className="flex items-center gap-3">
                    <Globe className="w-6 h-6 text-gray-600" />
                    <h3 className="text-lg font-semibold text-gray-900">Online Orders</h3>
                    <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-sm font-medium">
                      {statsLoading ? '...' : filteredOrders.length}
                    </span>
                  </div>

                  {/* Search and Filters */}
                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                    {/* Status Filter Buttons */}
                    <div className="flex flex-wrap gap-2">
                      {(['All', 'Pending', 'Approved', 'Processing', 'Confirmed', 'Shipped', 'Delivered', 'Cancelled'] as const).map((status) => (
                        <button
                          key={status}
                          onClick={() => setStatusFilter(status as OnlineOrderStatus | 'All')}
                          className={`px-3 py-1 rounded-lg font-medium text-xs transition duration-200 ${
                            statusFilter === status
                              ? 'bg-indigo-600 text-white shadow-lg'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }`}
                        >
                          {status} {status !== 'All' && `(${
                            status === 'Pending' ? orders.filter(order => order.status === 'Pending').length :
                            status === 'Approved' ? orders.filter(order => order.status === 'Approved').length :
                            status === 'Processing' ? orders.filter(order => order.status === 'Processing').length :
                            status === 'Confirmed' ? orders.filter(order => order.status === 'Confirmed').length :
                            status === 'Shipped' ? orders.filter(order => order.status === 'Shipped').length :
                            status === 'Delivered' ? orders.filter(order => order.status === 'Delivered').length :
                            status === 'Cancelled' ? orders.filter(order => order.status === 'Cancelled').length : 0
                          })`}
                        </button>
                      ))}
                    </div>

                    {/* Platform Filter */}
                    <select
                      value={platformFilter}
                      onChange={(e) => setPlatformFilter(e.target.value as OnlineOrder['platform'] | 'All')}
                      className="px-3 py-1 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                    >
                      <option value="All">All Platforms</option>
                      <option value="Shopify">Shopify</option>
                      <option value="Website">Website</option>
                      <option value="Mobile App">Mobile App</option>
                      <option value="Social Media">Social Media</option>
                    </select>

                    {/* Search */}
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Search online orders..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 w-64 text-sm"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Responsive Table */}
              <ResponsiveTable
                columns={onlineOrderColumns}
                data={filteredOrders}
                renderRow={renderOnlineOrderRow}
                renderMobileCard={renderOnlineOrderMobileCard}
                loading={statsLoading}
                emptyState={
                  <div className="flex flex-col items-center py-12">
                    <Globe className="w-16 h-16 text-gray-300 mb-4" />
                    <p className="text-lg font-medium text-gray-500 mb-2">No online orders found</p>
                    <p className="text-sm text-gray-400">Try adjusting your search criteria or filters</p>
                  </div>
                }
              />
            </div>
          </div>
        </main>

        {/* Settings Modal */}
        {showSettings && (
          <SettingsModal
            isOpen={showSettings}
            onClose={() => setShowSettings(false)}
          />
        )}

        {/* Enhanced Order Details Modal */}
        <OnlineOrderDetailsModal
          order={selectedOrder}
          isOpen={!!selectedOrder}
          onClose={() => setSelectedOrder(null)}
          onApprove={handleApproveOrder}
          onDeny={handleDenyOrder}
        />

        {/* Cancellation Modal */}
        <CancellationModal
          isOpen={showCancellationModal}
          onClose={() => {
            setShowCancellationModal(false);
            setOrderToCancel(null);
          }}
          onConfirm={(reason, notes) => {
            if (orderToCancel) {
              handleCancelOrder(orderToCancel.id, reason, notes);
            }
            setShowCancellationModal(false);
            setOrderToCancel(null);
            setSelectedOrder(null); // Close the order details modal too
          }}
          orderName={orderToCancel?.id || ''}
        />
      </div>
    </PageTransition>
  );
};

// Platform icon mapping
const getPlatformIcon = (platform: OnlineOrder['platform']) => {
  switch (platform) {
    case 'Website':
      return <Monitor className="w-4 h-4" />;
    case 'Mobile App':
      return <Smartphone className="w-4 h-4" />;
    case 'Social Media':
      return <Globe className="w-4 h-4" />;
    case 'Shopify':
      return <ShoppingCart className="w-4 h-4" />;
    default:
      return <Globe className="w-4 h-4" />;
  }
};

// Status color mapping
const getStatusColor = (status: OnlineOrderStatus) => {
  switch (status) {
    case 'Pending':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'Approved':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'Processing':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'Confirmed':
      return 'bg-indigo-100 text-indigo-800 border-indigo-200';
    case 'Shipped':
      return 'bg-purple-100 text-purple-800 border-purple-200';
    case 'Delivered':
      return 'bg-emerald-100 text-emerald-800 border-emerald-200';
    case 'Cancelled':
      return 'bg-red-100 text-red-800 border-red-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

// Enhanced Order Details Modal Component
const OnlineOrderDetailsModal: React.FC<{
  order: OnlineOrder | null;
  isOpen: boolean;
  onClose: () => void;
  onApprove: (orderId: string) => void;
  onDeny: (order: OnlineOrder) => void;
}> = ({ order, isOpen, onClose, onApprove, onDeny }) => {
  const onlineOrdersT = useTranslations('onlineOrders');

  if (!order) return null;

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const handleApprove = () => {
    onApprove(order.id);
    onClose();
  };

  const handleDeny = () => {
    onDeny(order);
    // Don't close the modal here - it will be closed after cancellation confirmation
  };

  return (
    <CompactModal
      isOpen={isOpen}
      onClose={onClose}
      title={`${onlineOrdersT('orderDetails')} - ${order.id}`}
      maxWidth="max-w-4xl"
    >
      <div className="space-y-6">
        {/* Order Header */}
        <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-6 border border-indigo-100">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-full flex items-center justify-center">
                <ShoppingCart className="w-8 h-8 text-indigo-600" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                  {order.id}
                  <button
                    onClick={() => copyToClipboard(order.id)}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </h3>
                <p className="text-gray-600">{order.customer}</p>
                <div className="flex items-center gap-2 mt-1">
                  {getPlatformIcon(order.platform)}
                  <span className="text-sm text-gray-500">{order.platform}</span>
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-gray-900">{order.total} {order.currency}</div>
              <span className={`inline-block px-3 py-1 text-sm font-medium rounded-full border ${getStatusColor(order.status)}`}>
                {order.status}
              </span>
            </div>
          </div>
        </div>

        {/* Order Information Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Customer Information */}
          <div className="bg-white border border-gray-200 rounded-xl p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <User className="w-5 h-5 text-indigo-600" />
              Customer Information
            </h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-600 flex items-center gap-2">
                  <User className="w-4 h-4" />
                  {onlineOrdersT('customer')}:
                </span>
                <span className="font-medium text-gray-900">{order.customer}</span>
              </div>
              {order.email && (
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 flex items-center gap-2">
                    <Mail className="w-4 h-4" />
                    {onlineOrdersT('email')}:
                  </span>
                  <span className="font-medium text-gray-900">{order.email}</span>
                </div>
              )}
              {order.phone && (
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 flex items-center gap-2">
                    <Phone className="w-4 h-4" />
                    {onlineOrdersT('phone')}:
                  </span>
                  <span className="font-medium text-gray-900">{order.phone}</span>
                </div>
              )}
            </div>
          </div>

          {/* Order Details */}
          <div className="bg-white border border-gray-200 rounded-xl p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Package className="w-5 h-5 text-indigo-600" />
              Order Details
            </h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-600 flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  {onlineOrdersT('orderDate')}:
                </span>
                <span className="font-medium text-gray-900">{order.orderDate}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600 flex items-center gap-2">
                  <CreditCard className="w-4 h-4" />
                  {onlineOrdersT('paymentMethod')}:
                </span>
                <span className="font-medium text-gray-900">{order.paymentMethod}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600 flex items-center gap-2">
                  <Tag className="w-4 h-4" />
                  Financial Status:
                </span>
                <span className="font-medium text-gray-900 capitalize">{order.financialStatus}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600 flex items-center gap-2">
                  <Truck className="w-4 h-4" />
                  Fulfillment Status:
                </span>
                <span className="font-medium text-gray-900 capitalize">{order.fulfillmentStatus}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Delivery Address */}
        <div className="bg-white border border-gray-200 rounded-xl p-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <MapPin className="w-5 h-5 text-indigo-600" />
            {onlineOrdersT('deliveryAddress')}
          </h4>
          <p className="text-gray-700 leading-relaxed">{order.deliveryAddress}</p>
        </div>

        {/* Order Items */}
        <div className="bg-white border border-gray-200 rounded-xl p-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <Package className="w-5 h-5 text-indigo-600" />
            {onlineOrdersT('items')} ({order.items.length})
          </h4>
          <div className="space-y-3">
            {order.items.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <h5 className="font-medium text-gray-900">{item.name}</h5>
                  <p className="text-sm text-gray-600">
                    {onlineOrdersT('quantity')}: {item.quantity} × {item.price} {order.currency}
                  </p>
                </div>
                <div className="text-right">
                  <span className="font-bold text-gray-900">
                    {(parseFloat(item.price) * item.quantity).toFixed(2)} {order.currency}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {/* Order Total */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between text-lg font-bold">
              <span className="text-gray-900">{onlineOrdersT('total')}:</span>
              <span className="text-indigo-600">{order.total} {order.currency}</span>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        {(order.notes || order.source || order.tags?.length) && (
          <div className="bg-white border border-gray-200 rounded-xl p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <FileText className="w-5 h-5 text-indigo-600" />
              Additional Information
            </h4>
            <div className="space-y-3">
              {order.notes && (
                <div>
                  <span className="text-gray-600 font-medium">{onlineOrdersT('notes')}:</span>
                  <p className="text-gray-700 mt-1">{order.notes}</p>
                </div>
              )}
              {order.source && (
                <div>
                  <span className="text-gray-600 font-medium">{onlineOrdersT('source')}:</span>
                  <span className="text-gray-700 ml-2">{order.source}</span>
                </div>
              )}
              {order.tags && order.tags.length > 0 && (
                <div>
                  <span className="text-gray-600 font-medium">Tags:</span>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {order.tags.map((tag, index) => (
                      <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}


      </div>
    </CompactModal>
  );
};

// Cancellation Modal Component
const CancellationModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (reason: string, notes?: string) => void;
  orderName: string;
}> = ({ isOpen, onClose, onConfirm, orderName }) => {
  const [selectedReason, setSelectedReason] = useState('');
  const [notes, setNotes] = useState('');

  // Shopify standard cancellation reasons
  const cancellationReasons = [
    { value: 'customer', label: 'Customer requested cancellation' },
    { value: 'inventory', label: 'Items unavailable' },
    { value: 'fraud', label: 'Fraudulent order' },
    { value: 'declined', label: 'Payment declined' },
    { value: 'other', label: 'Other reason' }
  ];

  const handleConfirm = () => {
    if (!selectedReason) {
      alert('Please select a cancellation reason');
      return;
    }
    onConfirm(selectedReason, notes);
    // Reset form
    setSelectedReason('');
    setNotes('');
  };

  const handleClose = () => {
    setSelectedReason('');
    setNotes('');
    onClose();
  };

  return (
    <CompactModal
      isOpen={isOpen}
      onClose={handleClose}
      title={`Cancel Order ${orderName}`}
      maxWidth="max-w-lg"
    >
      <div className="space-y-6">
        {/* Warning Message */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start gap-3">
          <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="text-sm font-medium text-red-800">Confirm Cancellation</h4>
            <p className="text-sm text-red-700 mt-1">
              This action will cancel the order and cannot be undone. The customer will be notified.
            </p>
          </div>
        </div>

        {/* Cancellation Reason */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Cancellation Reason *
          </label>
          <div className="space-y-2">
            {cancellationReasons.map((reason) => (
              <label key={reason.value} className="flex items-center">
                <input
                  type="radio"
                  name="cancellationReason"
                  value={reason.value}
                  checked={selectedReason === reason.value}
                  onChange={(e) => setSelectedReason(e.target.value)}
                  className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
                />
                <span className="ml-3 text-sm text-gray-700">{reason.label}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Additional Notes */}
        <div>
          <label htmlFor="cancellationNotes" className="block text-sm font-medium text-gray-700 mb-2">
            Additional Notes (Optional)
          </label>
          <textarea
            id="cancellationNotes"
            rows={3}
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Add any additional details about the cancellation..."
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-sm"
          />
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4 border-t border-gray-200">
          <button
            onClick={handleClose}
            className="flex-1 bg-gray-100 text-gray-700 py-3 px-6 rounded-lg font-medium hover:bg-gray-200 transition duration-200"
          >
            Cancel
          </button>
          <button
            onClick={handleConfirm}
            className="flex-1 bg-red-500 text-white py-3 px-6 rounded-lg font-medium hover:bg-red-600 transition duration-200 flex items-center justify-center gap-2"
          >
            <Ban className="w-4 h-4" />
            Confirm Cancellation
          </button>
        </div>
      </div>
    </CompactModal>
  );
};

export default OnlineOrdersPage;
