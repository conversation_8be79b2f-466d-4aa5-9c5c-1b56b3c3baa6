'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslations } from '@/hooks/useTranslations';
import { ResponsiveTable, ResponsiveTableRow, ResponsiveTableCell } from '@/components/ResponsiveTable';
import {
  ShoppingCart,
  Package,
  Users,
  TrendingUp,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Building2,
  Phone,
  Mail,
  MapPin,
  Calendar,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';

import { SettingsModal } from '@/components/SettingsModal';
import { PageTransition } from '@/components/PageTransition';
import { useNavigationTransition } from '@/hooks/useNavigationTransition';
import { Sidebar } from '@/components/Sidebar';
import { Header } from '@/components/Header';

// Types
type SupplierStatus = 'Active' | 'Inactive';
type PurchaseOrderStatus = 'Pending' | 'Approved' | 'Delivered' | 'Cancelled';

interface Supplier {
  id: string;
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  category: string;
  status: SupplierStatus;
  joinDate: string;
}

interface PurchaseOrderItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

interface PurchaseOrder {
  id: string;
  orderNumber: string;
  supplierId: string;
  supplierName: string;
  orderDate: string;
  deliveryDate: string;
  status: PurchaseOrderStatus;
  items: PurchaseOrderItem[];
  totalAmount: number;
  notes?: string;
}

// Mock data
const mockSuppliers: Supplier[] = [
  {
    id: '1',
    name: 'Al-Rashid Trading Co.',
    contactPerson: 'Ahmed Al-Rashid',
    email: '<EMAIL>',
    phone: '+966 50 123 4567',
    address: 'Riyadh, Saudi Arabia',
    category: 'Electronics',
    status: 'Active',
    joinDate: '2023-01-15'
  },
  {
    id: '2',
    name: 'Gulf Supplies Ltd.',
    contactPerson: 'Fatima Al-Zahra',
    email: '<EMAIL>',
    phone: '+966 55 987 6543',
    address: 'Jeddah, Saudi Arabia',
    category: 'Office Supplies',
    status: 'Active',
    joinDate: '2023-03-20'
  }
];

const mockPurchaseOrders: PurchaseOrder[] = [
  {
    id: '1',
    orderNumber: 'PO-2024-001',
    supplierId: '1',
    supplierName: 'Al-Rashid Trading Co.',
    orderDate: '2024-01-15',
    deliveryDate: '2024-01-25',
    status: 'Pending',
    totalAmount: 15000,
    items: [
      {
        id: '1',
        description: 'Laptops - Dell Inspiron',
        quantity: 10,
        unitPrice: 1200,
        totalPrice: 12000
      },
      {
        id: '2',
        description: 'Wireless Mice',
        quantity: 20,
        unitPrice: 150,
        totalPrice: 3000
      }
    ],
    notes: 'Urgent delivery required'
  }
];

// Status color mapping
const getStatusColor = (status: SupplierStatus | PurchaseOrderStatus) => {
  switch (status) {
    case 'Active':
    case 'Approved':
    case 'Delivered':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'Inactive':
    case 'Cancelled':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'Pending':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

// Main Purchasing Page Component
const PurchasePage: React.FC = () => {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const t = useTranslations('purchasing');

  const [activeItem, setActiveItem] = useState('purchasing');
  const [suppliers, setSuppliers] = useState<Supplier[]>(mockSuppliers);
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>(mockPurchaseOrders);
  const [activeTab, setActiveTab] = useState<'suppliers' | 'orders'>('suppliers');
  const [searchTerm, setSearchTerm] = useState('');
  const [supplierStatusFilter, setSupplierStatusFilter] = useState<SupplierStatus | 'All'>('All');
  const [purchaseOrderStatusFilter, setPurchaseOrderStatusFilter] = useState<PurchaseOrderStatus | 'All'>('All');
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [statsLoading, setStatsLoading] = useState(true);

  // Navigation transition hook
  const { navigateWithTransition } = useNavigationTransition({ animationType: 'fade' });

  // Navigation handler
  const handleNavigation = (path: string) => {
    navigateWithTransition(path);
  };

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login');
    }

    // Simulate loading stats
    const timer = setTimeout(() => {
      setStatsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [user, authLoading, router]);

  // Filter data based on search term and status
  const filteredSuppliers = suppliers.filter(supplier => {
    const matchesSearch = supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supplier.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supplier.category.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = supplierStatusFilter === 'All' || supplier.status === supplierStatusFilter;
    return matchesSearch && matchesStatus;
  });

  const filteredPurchaseOrders = purchaseOrders.filter(order => {
    const matchesSearch = order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.supplierName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = purchaseOrderStatusFilter === 'All' || order.status === purchaseOrderStatusFilter;
    return matchesSearch && matchesStatus;
  });

  // Calculate statistics
  const stats = {
    totalSuppliers: suppliers.length,
    activeSuppliers: suppliers.filter(s => s.status === 'Active').length,
    activePurchaseOrders: purchaseOrders.filter(po => po.status === 'Pending' || po.status === 'Approved').length,
    pendingDeliveries: purchaseOrders.filter(po => po.status === 'Approved').length,
    monthlySpend: purchaseOrders.reduce((sum, po) => sum + po.totalAmount, 0)
  };

  // Column definitions for suppliers table
  const supplierColumns = [
    { key: 'supplier', label: t('supplier'), className: 'min-w-[200px]' },
    { key: 'contactPerson', label: t('contactPerson'), className: 'min-w-[150px]' },
    { key: 'category', label: t('category'), className: 'min-w-[120px]' },
    { key: 'status', label: t('status'), className: 'min-w-[100px]' },
    { key: 'actions', label: t('actions'), className: 'min-w-[120px]' }
  ];

  // Column definitions for purchase orders table
  const purchaseOrderColumns = [
    { key: 'orderNumber', label: t('orderNumber'), className: 'min-w-[150px]' },
    { key: 'supplier', label: t('supplier'), className: 'min-w-[150px]' },
    { key: 'orderDate', label: t('orderDate'), className: 'min-w-[120px]' },
    { key: 'totalAmount', label: t('totalAmount'), className: 'min-w-[120px]' },
    { key: 'status', label: t('status'), className: 'min-w-[100px]' },
    { key: 'actions', label: t('actions'), className: 'min-w-[120px]' }
  ];

  // Render supplier table row
  const renderSupplierRow = (supplier: Supplier) => (
    <ResponsiveTableRow key={supplier.id}>
      <ResponsiveTableCell className="font-medium">
        <div className="flex items-center">
          <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
            <Building2 className="w-5 h-5 text-indigo-600" />
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">{supplier.name}</div>
            <div className="text-sm text-gray-500 flex items-center gap-1">
              <Mail className="w-3 h-3" />
              {supplier.email}
            </div>
          </div>
        </div>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <div className="text-sm text-gray-900">{supplier.contactPerson}</div>
        <div className="text-sm text-gray-500 flex items-center gap-1">
          <Phone className="w-3 h-3" />
          {supplier.phone}
        </div>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
          {supplier.category}
        </span>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(supplier.status)}`}>
          {supplier.status}
        </span>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <div className="flex items-center gap-2">
          <button className="text-indigo-600 hover:text-indigo-900 p-1 rounded">
            <Eye className="w-4 h-4" />
          </button>
          <button className="text-gray-600 hover:text-gray-900 p-1 rounded">
            <Edit className="w-4 h-4" />
          </button>
          <button className="text-red-600 hover:text-red-900 p-1 rounded">
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      </ResponsiveTableCell>
    </ResponsiveTableRow>
  );

  // Render purchase order table row
  const renderPurchaseOrderRow = (order: PurchaseOrder) => (
    <ResponsiveTableRow key={order.id}>
      <ResponsiveTableCell className="font-medium">
        <div className="text-sm font-medium text-gray-900">{order.orderNumber}</div>
        <div className="text-sm text-gray-500 flex items-center gap-1">
          <Calendar className="w-3 h-3" />
          Delivery: {order.deliveryDate}
        </div>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <div className="text-sm text-gray-900">{order.supplierName}</div>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <div className="text-sm text-gray-900">{order.orderDate}</div>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <div className="text-sm font-medium text-gray-900">
          SAR {order.totalAmount.toLocaleString()}
        </div>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(order.status)}`}>
          {order.status}
        </span>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <div className="flex items-center gap-2">
          <button className="text-indigo-600 hover:text-indigo-900 p-1 rounded">
            <Eye className="w-4 h-4" />
          </button>
          <button className="text-gray-600 hover:text-gray-900 p-1 rounded">
            <Edit className="w-4 h-4" />
          </button>
          <button className="text-red-600 hover:text-red-900 p-1 rounded">
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      </ResponsiveTableCell>
    </ResponsiveTableRow>
  );

  // Render supplier mobile card
  const renderSupplierMobileCard = (supplier: Supplier) => (
    <div key={supplier.id} className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
            <Building2 className="w-5 h-5 text-indigo-600" />
          </div>
          <div>
            <h3 className="text-sm font-semibold text-gray-900">{supplier.name}</h3>
            <p className="text-xs text-gray-500">{supplier.contactPerson}</p>
          </div>
        </div>
        <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(supplier.status)}`}>
          {supplier.status}
        </span>
      </div>

      <div className="space-y-2 text-xs text-gray-600">
        <div className="flex items-center gap-1">
          <Mail className="w-3 h-3" />
          <span>{supplier.email}</span>
        </div>
        <div className="flex items-center gap-1">
          <Phone className="w-3 h-3" />
          <span>{supplier.phone}</span>
        </div>
        <div className="flex items-center gap-1">
          <Package className="w-3 h-3" />
          <span>{supplier.category}</span>
        </div>
      </div>

      <div className="flex items-center justify-end gap-2 mt-3 pt-3 border-t border-gray-100">
        <button className="text-indigo-600 hover:text-indigo-900 p-1 rounded">
          <Eye className="w-4 h-4" />
        </button>
        <button className="text-gray-600 hover:text-gray-900 p-1 rounded">
          <Edit className="w-4 h-4" />
        </button>
        <button className="text-red-600 hover:text-red-900 p-1 rounded">
          <Trash2 className="w-4 h-4" />
        </button>
      </div>
    </div>
  );

  // Render purchase order mobile card
  const renderPurchaseOrderMobileCard = (order: PurchaseOrder) => (
    <div key={order.id} className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
            <ShoppingCart className="w-5 h-5 text-green-600" />
          </div>
          <div>
            <h3 className="text-sm font-semibold text-gray-900">{order.orderNumber}</h3>
            <p className="text-xs text-gray-500">{order.supplierName}</p>
          </div>
        </div>
        <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(order.status)}`}>
          {order.status}
        </span>
      </div>

      <div className="space-y-2 text-xs text-gray-600">
        <div className="flex items-center gap-1">
          <Calendar className="w-3 h-3" />
          <span>Order: {order.orderDate}</span>
        </div>
        <div className="flex items-center gap-1">
          <Calendar className="w-3 h-3" />
          <span>Delivery: {order.deliveryDate}</span>
        </div>
        <div className="flex items-center gap-1">
          <DollarSign className="w-3 h-3" />
          <span className="font-medium">SAR {order.totalAmount.toLocaleString()}</span>
        </div>
      </div>

      <div className="flex items-center justify-end gap-2 mt-3 pt-3 border-t border-gray-100">
        <button className="text-indigo-600 hover:text-indigo-900 p-1 rounded">
          <Eye className="w-4 h-4" />
        </button>
        <button className="text-gray-600 hover:text-gray-900 p-1 rounded">
          <Edit className="w-4 h-4" />
        </button>
        <button className="text-red-600 hover:text-red-900 p-1 rounded">
          <Trash2 className="w-4 h-4" />
        </button>
      </div>
    </div>
  );

  // Show loading spinner while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  // Don't render if user is not authenticated
  if (!user) {
    return null;
  }

  return (
    <PageTransition transitionKey="purchasing" animationType="fade">
      <div className="h-screen bg-gray-50 text-gray-900" data-page-content>
        <Sidebar
          activeItem={activeItem}
          setActiveItem={setActiveItem}
          isOpen={isSidebarOpen}
          setIsOpen={setIsSidebarOpen}
          onNavigate={handleNavigation}
          onOpenSettings={() => setShowSettings(true)}
        />
        <main className="lg:ml-64 flex flex-col h-full overflow-hidden">
          <Header
            showUserMenu={showUserMenu}
            setShowUserMenu={setShowUserMenu}
            setActiveItem={setActiveItem}
            setShowSettings={setShowSettings}
          />
          <div className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-8 pt-25">
            {/* Creative Add New Section */}
            <div className="relative bg-gradient-to-r from-indigo-600 to-purple-600 rounded-3xl p-8 mb-8 overflow-hidden">
              {/* Background decorative elements */}
              <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"></div>
              <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
              
              <div className="relative z-10 flex flex-col md:flex-row items-center justify-between">
                <div className="mb-6 md:mb-0 text-white">
                  <h2 className="text-3xl font-bold mb-2">{t('management')}</h2>
                  <p className="text-lg opacity-90 mb-4">Manage suppliers and purchase orders efficiently</p>
                  <div className="flex items-center gap-4 text-sm opacity-80">
                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4" />
                      <span>Supplier Management</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <ShoppingCart className="w-4 h-4" />
                      <span>Purchase Orders</span>
                    </div>
                  </div>
                </div>

                <div className="flex gap-3">
                  <button
                    onClick={() => {/* Add supplier logic */}}
                    className="bg-white text-indigo-600 px-6 py-3 rounded-2xl font-semibold hover:bg-gray-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-2"
                  >
                    <Plus className="w-5 h-5" />
                    {t('addSupplier')}
                  </button>
                  <button
                    onClick={() => {/* Add purchase order logic */}}
                    className="bg-white/20 text-white px-6 py-3 rounded-2xl font-semibold hover:bg-white/30 transition-all duration-200 border border-white/30 flex items-center gap-2"
                  >
                    <ShoppingCart className="w-5 h-5" />
                    {t('addPurchaseOrder')}
                  </button>
                </div>
              </div>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('totalSuppliers')}</p>
                    <p className="text-3xl font-bold text-gray-900">{stats.totalSuppliers}</p>
                  </div>
                  <Users className="w-8 h-8 text-indigo-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('activePurchaseOrders')}</p>
                    <p className="text-3xl font-bold text-gray-900">{stats.activePurchaseOrders}</p>
                  </div>
                  <ShoppingCart className="w-8 h-8 text-green-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('pendingDeliveries')}</p>
                    <p className="text-3xl font-bold text-gray-900">{stats.pendingDeliveries}</p>
                  </div>
                  <Clock className="w-8 h-8 text-yellow-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('monthlySpend')}</p>
                    <p className="text-3xl font-bold text-gray-900">SAR {stats.monthlySpend.toLocaleString()}</p>
                  </div>
                  <DollarSign className="w-8 h-8 text-purple-600" />
                </div>
              </div>
            </div>

            {/* Header with integrated search and filters */}
            <div className="bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden mb-6">
              <div className="p-4 sm:p-6 border-b border-gray-100">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                  <div className="flex items-center gap-3">
                    <ShoppingCart className="w-6 h-6 text-gray-600" />
                    <h3 className="text-lg font-semibold text-gray-900">{t('title')}</h3>
                    <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-sm font-medium">
                      {statsLoading ? '...' : (activeTab === 'suppliers' ? filteredSuppliers.length : filteredPurchaseOrders.length)}
                    </span>
                  </div>

                  {/* Tab Navigation and Search/Filter */}
                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                    {/* Tab Navigation */}
                    <div className="flex bg-gray-100 rounded-lg p-1">
                      <button
                        onClick={() => setActiveTab('suppliers')}
                        className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                          activeTab === 'suppliers'
                            ? 'bg-white text-indigo-600 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        {t('suppliers')}
                      </button>
                      <button
                        onClick={() => setActiveTab('orders')}
                        className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                          activeTab === 'orders'
                            ? 'bg-white text-indigo-600 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        {t('purchaseOrders')}
                      </button>
                    </div>

                    {/* Status Filter Buttons */}
                    <div className="flex flex-wrap gap-2">
                      {activeTab === 'suppliers' ? (
                        (['All', 'Active', 'Inactive'] as const).map((status) => (
                          <button
                            key={status}
                            onClick={() => setSupplierStatusFilter(status)}
                            className={`px-3 py-1 rounded-lg font-medium text-xs transition duration-200 ${
                              supplierStatusFilter === status
                                ? 'bg-indigo-600 text-white shadow-lg'
                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            }`}
                          >
                            {status} {status !== 'All' && `(${
                              status === 'Active' ? suppliers.filter(s => s.status === 'Active').length :
                              status === 'Inactive' ? suppliers.filter(s => s.status === 'Inactive').length : 0
                            })`}
                          </button>
                        ))
                      ) : (
                        (['All', 'Pending', 'Approved', 'Delivered', 'Cancelled'] as const).map((status) => (
                          <button
                            key={status}
                            onClick={() => setPurchaseOrderStatusFilter(status)}
                            className={`px-3 py-1 rounded-lg font-medium text-xs transition duration-200 ${
                              purchaseOrderStatusFilter === status
                                ? 'bg-indigo-600 text-white shadow-lg'
                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            }`}
                          >
                            {status} {status !== 'All' && `(${
                              status === 'Pending' ? purchaseOrders.filter(po => po.status === 'Pending').length :
                              status === 'Approved' ? purchaseOrders.filter(po => po.status === 'Approved').length :
                              status === 'Delivered' ? purchaseOrders.filter(po => po.status === 'Delivered').length :
                              status === 'Cancelled' ? purchaseOrders.filter(po => po.status === 'Cancelled').length : 0
                            })`}
                          </button>
                        ))
                      )}
                    </div>

                    {/* Search */}
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder={activeTab === 'suppliers' ? 'Search suppliers...' : 'Search purchase orders...'}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 w-64 text-sm"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Responsive Tables */}
            {activeTab === 'suppliers' ? (
              <ResponsiveTable
                columns={supplierColumns}
                data={filteredSuppliers}
                renderRow={renderSupplierRow}
                renderMobileCard={renderSupplierMobileCard}
                loading={statsLoading}
                emptyState={
                  <div className="flex flex-col items-center py-12">
                    <Users className="w-16 h-16 text-gray-300 mb-4" />
                    <p className="text-lg font-medium text-gray-500 mb-2">{t('noSuppliersFound')}</p>
                    <p className="text-sm text-gray-400">{t('createFirstSupplier')}</p>
                  </div>
                }
              />
            ) : (
              <ResponsiveTable
                columns={purchaseOrderColumns}
                data={filteredPurchaseOrders}
                renderRow={renderPurchaseOrderRow}
                renderMobileCard={renderPurchaseOrderMobileCard}
                loading={statsLoading}
                emptyState={
                  <div className="flex flex-col items-center py-12">
                    <ShoppingCart className="w-16 h-16 text-gray-300 mb-4" />
                    <p className="text-lg font-medium text-gray-500 mb-2">{t('noPurchaseOrdersFound')}</p>
                    <p className="text-sm text-gray-400">{t('createFirstPurchaseOrder')}</p>
                  </div>
                }
              />
            )}
          </div>
        </main>

        {/* Settings Modal */}
        <SettingsModal
          isOpen={showSettings}
          onClose={() => setShowSettings(false)}
        />
      </div>
    </PageTransition>
  );
};

export default PurchasePage;
