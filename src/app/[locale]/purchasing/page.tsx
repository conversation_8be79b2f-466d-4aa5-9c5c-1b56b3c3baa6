'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslations } from '@/hooks/useTranslations';
import { ResponsiveTable, ResponsiveTableRow, ResponsiveTableCell } from '@/components/ResponsiveTable';
import {
  ShoppingCart,
  Package,
  Users,
  TrendingUp,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Building2,
  Phone,
  Mail,
  MapPin,
  Calendar,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Check,
  Ban,
  AlertTriangle,
  Globe
} from 'lucide-react';

import { SettingsModal } from '@/components/SettingsModal';
import { PageTransition } from '@/components/PageTransition';
import { CompactModal } from '@/components/CompactModal';
import { useNavigationTransition } from '@/hooks/useNavigationTransition';
import { Sidebar } from '@/components/Sidebar';
import { Header } from '@/components/Header';
import { useOrders, ApprovedOrder } from '@/contexts/OrdersContext';
import { NotificationModal } from '@/components/NotificationModal';





// Status color mapping for approved orders
const getStatusColor = (status: string) => {
  switch (status) {
    case 'Approved':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'Cancelled':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'Pending':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

// Cancellation Form Component
interface CancellationFormProps {
  order: ApprovedOrder;
  onCancel: (orderId: string, reason: string, notes?: string) => void;
  onClose: () => void;
}

const CancellationForm: React.FC<CancellationFormProps> = ({ order, onCancel, onClose }) => {
  const [selectedReason, setSelectedReason] = useState('');
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const cancellationReasons = [
    'Customer requested cancellation',
    'Items unavailable',
    'Fraudulent order',
    'Payment declined',
    'Other reason'
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedReason) {
      alert('Please select a cancellation reason');
      return;
    }

    setIsSubmitting(true);
    try {
      onCancel(order.id, selectedReason, notes);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Warning Message */}
      <div className="flex items-start gap-3 p-4 bg-red-50 border border-red-200 rounded-lg">
        <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
        <div>
          <h4 className="text-sm font-medium text-red-800">Cancel Order #{order.id}</h4>
          <p className="text-sm text-red-700 mt-1">
            This action will cancel the order for {order.customer}. This action cannot be undone.
          </p>
        </div>
      </div>

      {/* Cancellation Reason */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Cancellation Reason <span className="text-red-500">*</span>
        </label>
        <div className="space-y-2">
          {cancellationReasons.map((reason) => (
            <label key={reason} className="flex items-center">
              <input
                type="radio"
                name="cancellationReason"
                value={reason}
                checked={selectedReason === reason}
                onChange={(e) => setSelectedReason(e.target.value)}
                className="w-4 h-4 text-red-600 border-gray-300 focus:ring-red-500"
              />
              <span className="ml-2 text-sm text-gray-700">{reason}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Additional Notes */}
      <div>
        <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
          Additional Notes (Optional)
        </label>
        <textarea
          id="notes"
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-sm"
          placeholder="Add any additional information about the cancellation..."
        />
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3 pt-4 border-t border-gray-200">
        <button
          type="button"
          onClick={onClose}
          className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
        >
          Keep Order
        </button>
        <button
          type="submit"
          disabled={!selectedReason || isSubmitting}
          className="flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting ? 'Cancelling...' : 'Cancel Order'}
        </button>
      </div>
    </form>
  );
};

// Main Purchasing Page Component
const PurchasePage: React.FC = () => {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const t = useTranslations('purchasing');
  const { approvedOrders, updateOrderStatus, removeOrder, moveToMainOrders } = useOrders();

  const [activeItem, setActiveItem] = useState('purchasing');
  const [searchTerm, setSearchTerm] = useState('');
  const [notification, setNotification] = useState<{
    isOpen: boolean;
    type: 'success' | 'error' | 'warning' | 'info';
    title: string;
    message: string;
  }>({
    isOpen: false,
    type: 'info',
    title: '',
    message: ''
  });
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [statsLoading, setStatsLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<ApprovedOrder | null>(null);
  const [showCancellationModal, setShowCancellationModal] = useState(false);
  const [orderToCancel, setOrderToCancel] = useState<ApprovedOrder | null>(null);

  // Navigation transition hook
  const { navigateWithTransition } = useNavigationTransition({ animationType: 'fade' });

  // Navigation handler
  const handleNavigation = (path: string) => {
    navigateWithTransition(path);
  };

  // Show notification helper
  const showNotification = (type: 'success' | 'error' | 'warning' | 'info', title: string, message: string) => {
    setNotification({
      isOpen: true,
      type,
      title,
      message
    });
  };

  // Close notification
  const closeNotification = () => {
    setNotification(prev => ({ ...prev, isOpen: false }));
  };

  // Handle approved order approval - move to main orders
  const handleApproveApprovedOrder = async (orderId: string) => {
    try {
      const success = await moveToMainOrders(orderId);

      if (success) {
        showNotification(
          'success',
          'Order Moved Successfully! 🎉',
          `Order ${orderId} has been approved and moved to the main orders system. It will now appear in the core orders page for delivery processing.`
        );
      } else {
        throw new Error('Failed to move order to main system');
      }
    } catch (error) {
      console.error('Error approving order:', error);
      showNotification(
        'error',
        'Approval Failed ❌',
        `Unable to move order ${orderId} to the main orders system. Please try again or contact support.`
      );
    }
  };

  // Handle approved order denial (open cancellation modal)
  const handleDenyApprovedOrder = (order: ApprovedOrder) => {
    setOrderToCancel(order);
    setShowCancellationModal(true);
  };

  // Handle approved order cancellation confirmation
  const handleCancelApprovedOrder = (orderId: string, reason: string, notes?: string) => {
    updateOrderStatus(orderId, 'Cancelled');
    console.log(`Approved order ${orderId} cancelled: ${reason}${notes ? ` - ${notes}` : ''}`);
  };

  // Handle order click to open details modal
  const handleOrderClick = (order: ApprovedOrder) => setSelectedOrder(order);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login');
    }

    // Simulate loading stats
    const timer = setTimeout(() => {
      setStatsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [user, authLoading, router]);

  // Filter approved orders based on search term
  const filteredApprovedOrders = approvedOrders.filter(order => {
    const matchesSearch = order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.platform.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  // Calculate statistics
  const stats = {
    approvedOrders: approvedOrders.length,
    pendingApprovedOrders: approvedOrders.filter(ao => ao.status === 'Pending').length,
    totalValue: approvedOrders.reduce((sum, order) => sum + parseFloat(order.total), 0)
  };

  // Column definitions for approved orders table
  const approvedOrderColumns = [
    { key: 'orderId', label: 'Order ID', className: 'min-w-[150px]' },
    { key: 'customer', label: 'Customer', className: 'min-w-[150px]' },
    { key: 'platform', label: 'Platform', className: 'min-w-[100px]' },
    { key: 'total', label: 'Total', className: 'min-w-[120px]' },
    { key: 'status', label: 'Status', className: 'min-w-[100px]' },
    { key: 'actions', label: 'Actions', className: 'min-w-[120px]' }
  ];









  // Render approved order table row
  const renderApprovedOrderRow = (order: ApprovedOrder) => (
    <ResponsiveTableRow key={order.id} onClick={() => handleOrderClick(order)}>
      <ResponsiveTableCell className="font-medium">
        <div className="text-sm font-medium text-gray-900">#{order.id}</div>
        <div className="text-sm text-gray-500 flex items-center gap-1">
          <Calendar className="w-3 h-3" />
          Approved: {new Date(order.approvedDate).toLocaleDateString()}
        </div>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <div className="text-sm text-gray-900">{order.customer}</div>
        <div className="text-sm text-gray-500">{order.email}</div>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <div className="flex items-center gap-1">
          <Globe className="w-3 h-3 text-gray-400" />
          <span className="text-sm text-gray-900">{order.platform}</span>
        </div>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <div className="text-sm font-medium text-gray-900">
          {order.currency} {order.total}
        </div>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(order.status as any)}`}>
          {order.status}
        </span>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <div className="flex items-center gap-2">
          {order.status === 'Pending' ? (
            <>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleApproveApprovedOrder(order.id);
                }}
                className="group relative bg-gradient-to-r from-emerald-500 to-green-500 text-white p-2.5 rounded-xl hover:from-emerald-600 hover:to-green-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 active:translate-y-0"
                title="Approve Order"
              >
                <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 rounded-xl transition-opacity duration-200"></div>
                <Check className="w-4 h-4 relative z-10" />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleDenyApprovedOrder(order);
                }}
                className="group relative bg-gradient-to-r from-red-500 to-rose-500 text-white p-2.5 rounded-xl hover:from-red-600 hover:to-rose-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 active:translate-y-0"
                title="Deny Order"
              >
                <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 rounded-xl transition-opacity duration-200"></div>
                <Ban className="w-4 h-4 relative z-10" />
              </button>
            </>
          ) : (
            <span className="text-xs text-gray-500 px-2 py-1 bg-gray-100 rounded-full">
              {order.status}
            </span>
          )}
        </div>
      </ResponsiveTableCell>
    </ResponsiveTableRow>
  );

  // Render approved order mobile card
  const renderApprovedOrderMobileCard = (order: ApprovedOrder) => (
    <div key={order.id} className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow" onClick={() => handleOrderClick(order)}>
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
            <CheckCircle className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <h3 className="text-sm font-semibold text-gray-900">#{order.id}</h3>
            <p className="text-xs text-gray-500">{order.customer}</p>
          </div>
        </div>
        <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(order.status as any)}`}>
          {order.status}
        </span>
      </div>

      <div className="space-y-2 text-xs text-gray-600">
        <div className="flex items-center gap-1">
          <Globe className="w-3 h-3" />
          <span>{order.platform}</span>
        </div>
        <div className="flex items-center gap-1">
          <Calendar className="w-3 h-3" />
          <span>Approved: {new Date(order.approvedDate).toLocaleDateString()}</span>
        </div>
        <div className="flex items-center gap-1">
          <DollarSign className="w-3 h-3" />
          <span className="font-medium">{order.currency} {order.total}</span>
        </div>
      </div>

      <div className="flex items-center justify-end gap-2 mt-3 pt-3 border-t border-gray-100">
        {order.status === 'Pending' ? (
          <>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleApproveApprovedOrder(order.id);
              }}
              className="group relative bg-gradient-to-r from-emerald-500 to-green-500 text-white p-2.5 rounded-xl hover:from-emerald-600 hover:to-green-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 active:translate-y-0"
              title="Approve Order"
            >
              <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 rounded-xl transition-opacity duration-200"></div>
              <Check className="w-4 h-4 relative z-10" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleDenyApprovedOrder(order);
              }}
              className="group relative bg-gradient-to-r from-red-500 to-rose-500 text-white p-2.5 rounded-xl hover:from-red-600 hover:to-rose-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 active:translate-y-0"
              title="Deny Order"
            >
              <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 rounded-xl transition-opacity duration-200"></div>
              <Ban className="w-4 h-4 relative z-10" />
            </button>
          </>
        ) : (
          <span className="text-xs text-gray-500 px-2 py-1 bg-gray-100 rounded-full">
            {order.status}
          </span>
        )}
      </div>
    </div>
  );

  // Show loading spinner while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  // Don't render if user is not authenticated
  if (!user) {
    return null;
  }

  return (
    <PageTransition transitionKey="purchasing" animationType="fade">
      <div className="h-screen bg-gray-50 text-gray-900" data-page-content>
        <Sidebar
          activeItem={activeItem}
          setActiveItem={setActiveItem}
          isOpen={isSidebarOpen}
          setIsOpen={setIsSidebarOpen}
          onNavigate={handleNavigation}
          onOpenSettings={() => setShowSettings(true)}
        />
        <main className="lg:ml-64 flex flex-col h-full overflow-hidden">
          <Header
            showUserMenu={showUserMenu}
            setShowUserMenu={setShowUserMenu}
            setActiveItem={setActiveItem}
            setShowSettings={setShowSettings}
          />
          <div className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-8 pt-25">
            {/* Header Section */}
            <div className="relative bg-gradient-to-r from-indigo-600 to-purple-600 rounded-3xl p-8 mb-8 overflow-hidden">
              {/* Background decorative elements */}
              <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"></div>
              <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>

              <div className="relative z-10 flex flex-col md:flex-row items-center justify-between">
                <div className="mb-6 md:mb-0 text-white">
                  <h2 className="text-3xl font-bold mb-2">Approved Orders Management</h2>
                  <p className="text-lg opacity-90 mb-4">Process and manage approved orders from online platforms</p>
                  <div className="flex items-center gap-4 text-sm opacity-80">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4" />
                      <span>Order Processing</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Package className="w-4 h-4" />
                      <span>Fulfillment Management</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Approved Orders</p>
                    <p className="text-3xl font-bold text-gray-900">{stats.approvedOrders}</p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pending Processing</p>
                    <p className="text-3xl font-bold text-gray-900">{stats.pendingApprovedOrders}</p>
                  </div>
                  <Clock className="w-8 h-8 text-yellow-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Value</p>
                    <p className="text-3xl font-bold text-gray-900">
                      EGP {approvedOrders.reduce((sum, order) => sum + parseFloat(order.total), 0).toLocaleString()}
                    </p>
                  </div>
                  <DollarSign className="w-8 h-8 text-purple-600" />
                </div>
              </div>
            </div>

            {/* Header with integrated search and filters */}
            <div className="bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden mb-6">
              <div className="p-4 sm:p-6 border-b border-gray-100">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-6 h-6 text-green-600" />
                    <h3 className="text-lg font-semibold text-gray-900">Approved Orders</h3>
                    <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-sm font-medium">
                      {approvedOrders.length}
                    </span>
                  </div>

                  {/* Tab Navigation and Search/Filter */}
                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">


                    {/* Status Filter Buttons */}
                    <div className="flex flex-wrap gap-2">
                      {(['All', 'Pending', 'Approved', 'Cancelled'] as const).map((status) => (
                        <button
                          key={status}
                          className={`px-3 py-1 rounded-lg font-medium text-xs transition duration-200 ${
                            status === 'All'
                              ? 'bg-indigo-600 text-white shadow-lg'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }`}
                        >
                          {status} {status !== 'All' && `(${
                            status === 'Pending' ? approvedOrders.filter(ao => ao.status === 'Pending').length :
                            status === 'Approved' ? approvedOrders.filter(ao => ao.status === 'Approved').length :
                            status === 'Cancelled' ? approvedOrders.filter(ao => ao.status === 'Cancelled').length : 0
                          })`}
                        </button>
                      ))}
                    </div>

                    {/* Search */}
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Search approved orders..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 w-64 text-sm"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Approved Orders Table */}
            <ResponsiveTable
              columns={approvedOrderColumns}
              data={filteredApprovedOrders}
              renderRow={renderApprovedOrderRow}
              renderMobileCard={renderApprovedOrderMobileCard}
              loading={false}
              emptyState={
                <div className="flex flex-col items-center py-12">
                  <CheckCircle className="w-16 h-16 text-gray-300 mb-4" />
                  <p className="text-lg font-medium text-gray-500 mb-2">No approved orders found</p>
                  <p className="text-sm text-gray-400">Approved orders from online orders will appear here</p>
                </div>
              }
            />
          </div>
        </main>

        {/* Settings Modal */}
        <SettingsModal
          isOpen={showSettings}
          onClose={() => setShowSettings(false)}
        />

        {/* Order Details Modal */}
        {selectedOrder && (
          <CompactModal
            isOpen={!!selectedOrder}
            onClose={() => setSelectedOrder(null)}
            title={`Order #${selectedOrder.id}`}
            size="lg"
          >
            <div className="space-y-6">
              {/* Order Header */}
              <div className="flex items-center justify-between pb-4 border-b border-gray-200">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Order #{selectedOrder.id}</h3>
                  <p className="text-sm text-gray-500">Customer: {selectedOrder.customer}</p>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold text-gray-900">{selectedOrder.currency} {selectedOrder.total}</p>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(selectedOrder.status as any)}`}>
                    {selectedOrder.status}
                  </span>
                </div>
              </div>

              {/* Customer Information */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">Customer Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Email:</span>
                    <span className="ml-2 text-gray-900">{selectedOrder.email}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Phone:</span>
                    <span className="ml-2 text-gray-900">{selectedOrder.phone}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Platform:</span>
                    <span className="ml-2 text-gray-900">{selectedOrder.platform}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Payment:</span>
                    <span className="ml-2 text-gray-900">{selectedOrder.paymentMethod}</span>
                  </div>
                </div>
              </div>

              {/* Delivery Address */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">Delivery Address</h4>
                <p className="text-sm text-gray-700">{selectedOrder.deliveryAddress}</p>
              </div>

              {/* Order Items */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">Order Items</h4>
                <div className="space-y-3">
                  {selectedOrder.items.map((item, index) => (
                    <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                      <div>
                        <p className="text-sm font-medium text-gray-900">{item.name}</p>
                        <p className="text-xs text-gray-500">Quantity: {item.quantity}</p>
                      </div>
                      <p className="text-sm font-medium text-gray-900">{item.price}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Additional Information */}
              {(selectedOrder.notes || selectedOrder.tags?.length) && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Additional Information</h4>
                  {selectedOrder.notes && (
                    <p className="text-sm text-gray-700 mb-2">{selectedOrder.notes}</p>
                  )}
                  {selectedOrder.tags?.length && (
                    <div className="flex flex-wrap gap-1">
                      {selectedOrder.tags.map((tag, index) => (
                        <span key={index} className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </CompactModal>
        )}

        {/* Cancellation Modal */}
        {showCancellationModal && orderToCancel && (
          <CompactModal
            isOpen={showCancellationModal}
            onClose={() => {
              setShowCancellationModal(false);
              setOrderToCancel(null);
            }}
            title="Cancel Order"
            size="md"
          >
            <CancellationForm
              order={orderToCancel}
              onCancel={(orderId, reason, notes) => {
                handleCancelApprovedOrder(orderId, reason, notes);
                setShowCancellationModal(false);
                setOrderToCancel(null);
              }}
              onClose={() => {
                setShowCancellationModal(false);
                setOrderToCancel(null);
              }}
            />
          </CompactModal>
        )}

        {/* Enhanced Notification Modal */}
        <NotificationModal
          isOpen={notification.isOpen}
          onClose={closeNotification}
          type={notification.type}
          title={notification.title}
          message={notification.message}
        />
      </div>
    </PageTransition>
  );
};

export default PurchasePage;
