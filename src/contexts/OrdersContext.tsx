'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

// Types for approved orders in purchasing
export interface ApprovedOrderItem {
  name: string;
  quantity: number;
  price: string;
}

export interface ApprovedOrder {
  id: string;
  customer: string;
  email: string;
  phone: string;
  items: ApprovedOrderItem[];
  total: string;
  status: 'Pending' | 'Approved' | 'Cancelled';
  orderDate: string;
  platform: string;
  paymentMethod: string;
  deliveryAddress: string;
  financialStatus: string;
  fulfillmentStatus: string;
  currency: string;
  tags?: string[];
  notes?: string;
  source?: string;
  // Additional fields for purchasing context
  approvedDate: string;
  originalOrderId: string;
}

interface OrdersContextType {
  approvedOrders: ApprovedOrder[];
  addApprovedOrder: (order: ApprovedOrder) => void;
  updateOrderStatus: (orderId: string, status: 'Pending' | 'Approved' | 'Cancelled') => void;
  removeOrder: (orderId: string) => void;
  getOrderById: (orderId: string) => ApprovedOrder | undefined;
  moveToMainOrders: (orderId: string) => Promise<boolean>;
}

const OrdersContext = createContext<OrdersContextType | undefined>(undefined);

export const useOrders = () => {
  const context = useContext(OrdersContext);
  if (context === undefined) {
    throw new Error('useOrders must be used within an OrdersProvider');
  }
  return context;
};

interface OrdersProviderProps {
  children: ReactNode;
}

export const OrdersProvider: React.FC<OrdersProviderProps> = ({ children }) => {
  const [approvedOrders, setApprovedOrders] = useState<ApprovedOrder[]>([]);

  const addApprovedOrder = (order: ApprovedOrder) => {
    setApprovedOrders(prev => [...prev, order]);
  };

  const updateOrderStatus = (orderId: string, status: 'Pending' | 'Approved' | 'Cancelled') => {
    setApprovedOrders(prev => 
      prev.map(order => 
        order.id === orderId 
          ? { ...order, status }
          : order
      )
    );
  };

  const removeOrder = (orderId: string) => {
    setApprovedOrders(prev => prev.filter(order => order.id !== orderId));
  };

  const getOrderById = (orderId: string) => {
    return approvedOrders.find(order => order.id === orderId);
  };

  const moveToMainOrders = async (orderId: string): Promise<boolean> => {
    try {
      const order = getOrderById(orderId);
      if (!order) {
        console.error('Order not found:', orderId);
        return false;
      }

      // Convert ApprovedOrder to main Order format
      const mainOrderData = {
        orderId: order.id,
        customerName: order.customer,
        customerPhone: order.phone,
        customerAddress: order.deliveryAddress,
        orderType: 'Online Order',
        totalAmount: parseFloat(order.total.replace(/[^\d.]/g, '')),
        depositAmount: 0,
        status: 'Pending' as const,
        salesRep: 'Online Platform',
        items: order.items.map(item => ({
          name: item.name,
          quantity: item.quantity,
          price: parseFloat(item.price.replace(/[^\d.]/g, ''))
        }))
      };

      // Call API to create order in main system
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(mainOrderData)
      });

      if (!response.ok) {
        throw new Error('Failed to create order in main system');
      }

      // Remove from approved orders
      removeOrder(orderId);

      console.log(`Order ${orderId} moved to main orders successfully`);
      return true;
    } catch (error) {
      console.error('Error moving order to main orders:', error);
      return false;
    }
  };

  const value: OrdersContextType = {
    approvedOrders,
    addApprovedOrder,
    updateOrderStatus,
    removeOrder,
    getOrderById,
    moveToMainOrders
  };

  return (
    <OrdersContext.Provider value={value}>
      {children}
    </OrdersContext.Provider>
  );
};
