// Order State Manager for persisting order status changes locally
export interface OrderStateChange {
  orderId: string;
  status: string;
  timestamp: string;
  reason?: string;
  notes?: string;
}

const ORDER_STATES_KEY = 'online_order_states';

export class OrderStateManager {
  // Save order state change to localStorage
  static saveOrderState(orderId: string, status: string, reason?: string, notes?: string) {
    try {
      const states = this.getAllOrderStates();
      const stateChange: OrderStateChange = {
        orderId,
        status,
        timestamp: new Date().toISOString(),
        reason,
        notes
      };
      
      // Remove any existing state for this order
      const filteredStates = states.filter(state => state.orderId !== orderId);
      
      // Add the new state
      filteredStates.push(stateChange);
      
      localStorage.setItem(ORDER_STATES_KEY, JSON.stringify(filteredStates));
      console.log(`Order state saved: ${orderId} -> ${status}`);
    } catch (error) {
      console.error('Error saving order state:', error);
    }
  }

  // Get all order states from localStorage
  static getAllOrderStates(): OrderStateChange[] {
    try {
      const states = localStorage.getItem(ORDER_STATES_KEY);
      return states ? JSON.parse(states) : [];
    } catch (error) {
      console.error('Error loading order states:', error);
      return [];
    }
  }

  // Get specific order state
  static getOrderState(orderId: string): OrderStateChange | null {
    try {
      const states = this.getAllOrderStates();
      return states.find(state => state.orderId === orderId) || null;
    } catch (error) {
      console.error('Error getting order state:', error);
      return null;
    }
  }

  // Remove order state (when order is processed completely)
  static removeOrderState(orderId: string) {
    try {
      const states = this.getAllOrderStates();
      const filteredStates = states.filter(state => state.orderId !== orderId);
      localStorage.setItem(ORDER_STATES_KEY, JSON.stringify(filteredStates));
      console.log(`Order state removed: ${orderId}`);
    } catch (error) {
      console.error('Error removing order state:', error);
    }
  }

  // Apply saved states to orders array
  static applyStatesToOrders<T extends { id: string; status: string }>(orders: T[]): T[] {
    try {
      const states = this.getAllOrderStates();
      
      return orders.map(order => {
        const savedState = states.find(state => state.orderId === order.id);
        if (savedState) {
          return {
            ...order,
            status: savedState.status
          };
        }
        return order;
      });
    } catch (error) {
      console.error('Error applying states to orders:', error);
      return orders;
    }
  }

  // Clear all order states (for testing or reset)
  static clearAllStates() {
    try {
      localStorage.removeItem(ORDER_STATES_KEY);
      console.log('All order states cleared');
    } catch (error) {
      console.error('Error clearing order states:', error);
    }
  }

  // Get orders that have been approved (for purchasing page)
  static getApprovedOrders(): OrderStateChange[] {
    try {
      const states = this.getAllOrderStates();
      return states.filter(state => state.status === 'Approved');
    } catch (error) {
      console.error('Error getting approved orders:', error);
      return [];
    }
  }
}
