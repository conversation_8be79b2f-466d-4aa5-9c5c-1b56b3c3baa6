{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "deactivate": "Deactivate", "add": "Add", "update": "Update", "search": "Search...", "actions": "Actions", "status": "Status", "date": "Date", "time": "Time", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "total": "Total", "view": "View", "close": "Close", "confirm": "Confirm", "yes": "Yes", "no": "No", "all": "All", "ok": "OK", "success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "critical": "Critical"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "email": "Email Address", "password": "Password", "confirmPassword": "Confirm Password", "rememberMe": "Remember me", "forgotPassword": "Forgot Password?", "signIn": "Sign In", "signUp": "Sign Up", "createAccount": "Create Account", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "welcomeBack": "Welcome Back", "createNewAccount": "Create New Account", "loginToAccount": "Login to your account", "fillDetails": "Fill in your details to get started", "orContinueWith": "Or continue with", "continueWithGoogle": "Continue with Google", "signingInWithGoogle": "Signing in with Google...", "loginDescription": "Ready to streamline your delivery operations? Access your comprehensive dashboard to manage orders, track deliveries, and optimize your logistics with real-time insights and powerful analytics.", "errors": {"emailRequired": "Email is required", "passwordRequired": "Password is required", "loginError": "An error occurred during login", "invalidCredentials": "Invalid email or password", "googleSignInFailed": "Google sign-in failed", "signInCancelled": "Sign-in was cancelled", "popupBlocked": "Pop-up was blocked. Please allow pop-ups and try again"}}, "navigation": {"dashboard": "Dashboard", "orders": "Orders", "onlineOrders": "Online Orders", "purchasing": "Purchasing", "customers": "Customers", "team": "Team", "fleet": "Fleet", "reports": "Reports", "settings": "Settings", "profile": "Profile", "analyticsOverview": "Analytics & Overview", "onlineOperations": "Online Operations", "purchasingProcurement": "Purchasing & Procurement", "coreOperations": "Core Operations", "account": "Account"}, "onlineOrders": {"title": "Online Orders Management", "management": "Online Orders Management", "manageOnlineOrders": "Manage Online Orders", "trackAndManage": "Track and manage orders from all digital platforms", "websiteOrders": "Website Orders", "mobileApp": "Mobile App", "socialMedia": "Social Media", "syncOrders": "Sync Orders", "exportData": "Export Data", "allPlatforms": "All Platforms", "orderDetails": "Order Details", "orderId": "Order ID", "customer": "Customer", "email": "Email", "phone": "Phone", "total": "Total", "orderDate": "Order Date", "status": "Status", "platform": "Platform", "paymentMethod": "Payment Method", "deliveryAddress": "Delivery Address", "items": "Items", "quantity": "Quantity", "price": "Price", "financialStatus": "Financial Status", "fulfillmentStatus": "Fulfillment Status", "currency": "<PERSON><PERSON><PERSON><PERSON>", "notes": "Notes", "source": "Source", "noOrders": "No orders found", "loading": "Loading orders...", "error": "Error loading orders", "refresh": "Refresh", "viewDetails": "View Details", "markAsProcessed": "<PERSON> as Processed", "markAsShipped": "<PERSON> as Shipped", "markAsDelivered": "<PERSON> as Delivered", "totalOnlineOrders": "Total Online Orders", "pendingOrders": "Pending Orders", "approvedOrders": "Approved Orders", "cancelledOrders": "Cancelled Orders", "all": "All", "pending": "Pending", "approved": "Approved", "processing": "Processing", "confirmed": "Confirmed", "shipped": "Shipped", "delivered": "Delivered", "cancelled": "Cancelled", "actions": "Actions", "searchOnlineOrders": "Search online orders...", "noOnlineOrdersFound": "No online orders found", "tryAdjustingFilters": "Try adjusting your search criteria or filters", "customerInformation": "Customer Information", "additionalInformation": "Additional Information"}, "purchasing": {"title": "Purchasing & Procurement", "management": "Purchasing Management", "approvedOrdersManagement": "Approved Orders Management", "processAndManage": "Process and manage approved orders from online platforms", "orderProcessing": "Order Processing", "fulfillmentManagement": "Fulfillment Management", "suppliers": "Suppliers", "purchaseOrders": "Purchase Orders", "inventory": "Inventory", "addSupplier": "Add New Supplier", "addPurchaseOrder": "Create Purchase Order", "supplierName": "Supplier Name", "contactPerson": "Contact Person", "email": "Email", "phone": "Phone", "address": "Address", "category": "Category", "status": "Status", "active": "Active", "inactive": "Inactive", "pending": "Pending", "approved": "Approved", "delivered": "Delivered", "cancelled": "Cancelled", "orderNumber": "Order Number", "supplier": "Supplier", "orderDate": "Order Date", "deliveryDate": "Expected Delivery", "totalAmount": "Total Amount", "items": "Items", "quantity": "Quantity", "unitPrice": "Unit Price", "totalPrice": "Total Price", "description": "Description", "notes": "Notes", "actions": "Actions", "edit": "Edit", "delete": "Delete", "view": "View", "save": "Save", "cancel": "Cancel", "search": "Search suppliers, orders, items...", "noSuppliersFound": "No suppliers found", "noPurchaseOrdersFound": "No purchase orders found", "createFirstSupplier": "Create your first supplier to get started", "createFirstPurchaseOrder": "Create your first purchase order", "totalSuppliers": "Total Suppliers", "activePurchaseOrders": "Active Purchase Orders", "pendingDeliveries": "Pending Deliveries", "monthlySpend": "Monthly Spend", "noApprovedOrdersFound": "No approved orders found", "approvedOrdersWillAppear": "Approved orders from online orders will appear here"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome back", "welcomeMessage": "Here's what's happening with your Zawaya delivery today", "totalOrders": "Total Orders", "activeDeliveries": "Active Deliveries", "totalCustomers": "Total Customers", "revenue": "Revenue", "recentOrders": "Recent Orders", "activeDeliveriesTitle": "Active Deliveries", "viewAll": "View All", "viewMap": "View Map", "quickActions": "Quick Actions", "newOrder": "New Order", "newCustomer": "New Customer", "newTeamMember": "New Team Member", "newTruck": "New Truck", "deliveryPerformance": "Delivery Performance", "completed": "Completed", "failed": "Failed", "noActiveDeliveries": "No active deliveries", "activeDeliveriesWillAppear": "Active deliveries will appear here", "ordersTrend": "Orders Trend", "revenueTrend": "Revenue Trend", "deliverySuccessRate": "Delivery Success Rate", "avgDeliveryTime": "Avg Delivery Time", "customerSatisfaction": "Customer Satisfaction", "activeOrders": "Active Orders", "activeDrivers": "Active Drivers", "pendingOrders": "Pending Orders", "thisWeek": "% this week", "thisLastMonth": "% this last month", "orderId": "Order ID", "customer": "Customer", "status": "Status", "date": "Date", "amount": "Amount", "noOrdersYet": "No orders yet", "ordersWillAppear": "Orders will appear here once customers start placing them", "last7Days": "Last 7 Days", "last30Days": "Last 30 Days", "last3Months": "Last 3 Months", "createNewOrder": "Create New Order", "customerName": "Customer Name", "trendLabels": {"vsLastMonth": "vs last month", "vsLastWeek": "vs last week", "vsYesterday": "vs yesterday", "hoursVsLastMonth": "hours vs last month"}, "phoneNumber": "Phone Number", "deliveryAddress": "Delivery Address", "orderType": "Order Type", "notes": "Notes", "optional": "Optional", "createOrder": "Create Order", "cancel": "Cancel", "creating": "Creating...", "addNewMember": "Add New Member", "fullName": "Full Name", "emailAddress": "Email Address", "department": "Department", "role": "Role", "monthlySalary": "Monthly Salary", "startDate": "Start Date", "addMember": "Add Member", "adding": "Adding...", "quickOrder": "Quick Order", "createNewOrderQuickly": "Create a new order quickly and efficiently", "editOrder": "Edit Order", "updateOrderDetails": "Update order details", "orderStatus": "Order Status", "customerPhone": "Customer Phone", "customerAddress": "Customer Address", "deliveryAgent": "Delivery Agent", "salesRepresentative": "Sales Representative", "truck": "Truck", "driver": "Driver", "estimatedDeliveryTime": "Estimated Delivery Time", "orderDate": "Order Date", "selectOrderType": "Select order type", "selectDeliveryAgent": "Select delivery agent", "selectSalesRepresentative": "Select sales representative", "selectDriverFirst": "Select a driver first", "selectDriver": "Select driver", "selectDriverToSeeAvailableTrucks": "Select a driver to see available trucks", "enterPhoneNumber": "Enter phone number", "enterCustomerName": "Enter customer name", "enterCustomerAddress": "Enter customer address", "pending": "Pending", "close": "Close", "save": "Save", "saving": "Saving...", "dashboardStatus": {"processing": "Processing", "inTransit": "In Transit", "delivered": "Delivered", "cancelled": "Cancelled", "onTime": "On Time", "delayed": "Delayed", "early": "Early", "pending": "Pending", "started": "Started", "movedToSupplier": "Moved to Supplier", "arrivedAtSupplier": "Arrived at Supplier", "movingToCustomer": "Moving to Customer", "arrivedAtCustomer": "Arrived at Customer"}}, "orders": {"title": "Orders", "management": "Orders Management", "newOrder": "New Order", "orderDetails": "Order Details", "orderId": "Order ID", "customer": "Customer", "items": "Items", "deliveryAddress": "Delivery Address", "orderDate": "Order Date", "deliveryDate": "Delivery Date", "salesRep": "Sales Rep", "deliveryAgent": "Delivery Agent", "amount": "Amount", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "orderType": "Order Type", "date": "Date", "readyToCreate": "Ready to Create a New Order?", "startAdding": "Start adding orders to your delivery system with ease", "orderManagement": "Order Management", "deliveryTracking": "Delivery Tracking", "bulkExport": "Bulk Export", "oncomingOrders": "Oncoming Orders", "ongoingOrders": "Ongoing Orders", "completedOrders": "Completed Orders", "cancelledOrders": "Cancelled Orders", "all": "All", "pending": "Pending", "started": "Started", "delivered": "Delivered", "cancelled": "Cancelled", "searchOrders": "Search orders...", "noOrdersFound": "No orders found", "ordersWillAppearHere": "Orders will appear here when customers place them", "common": {"status": "Status"}, "status": {"pending": "Pending", "started": "Started", "movedToSupplier": "Moved to Supplier", "arrivedAtSupplier": "Arrived at Supplier", "movingToCustomer": "Moving to Customer", "arrivedAtCustomer": "Arrived at Customer", "delivered": "Delivered", "cancelled": "Cancelled"}, "orderTypes": {"paints": "Paints", "electricalSupplies": "Electrical Supplies", "bathroomFixtures": "Bathroom Fixtures", "plumbingSupplies": "Plumbing Supplies", "gypsumBoards": "Gypsum Boards", "ceramicTiles": "Ceramic Tiles", "airConditioning": "Air Conditioning", "hdfFlooring": "HDF Flooring", "tvs": "TVs", "homeAppliances": "Home Appliances"}}, "customers": {"title": "Customers", "newCustomer": "New Customer", "customerDetails": "Customer Details", "customerId": "Customer ID", "customerName": "Customer Name", "contactInfo": "Contact Information", "totalOrders": "Total Orders", "totalCustomers": "Total Customers", "lastOrder": "Last Order", "readyToAdd": "Ready to Add a New Customer?", "expandCustomerBase": "Expand your customer base and grow your business", "customerManagement": "Customer Management", "vipProgram": "VIP Program", "activeCustomers": "Active Customers", "vipCustomers": "VIP Customers", "inactiveCustomers": "Inactive Customers", "totalRevenue": "Total Revenue", "customer": "Customer", "statusLabel": "Status", "phone": "Phone", "orders": "Orders", "totalSpent": "Total Spent", "lastOrderDate": "Last Order", "noCustomersFound": "No customers found", "tryAdjustingSearch": "Try adjusting your search terms", "addFirstCustomer": "Add your first customer to get started", "ordersText": "orders", "joined": "Joined", "notes": "Notes", "searchCustomers": "Search customers...", "status": {"active": "Active", "inactive": "Inactive"}, "vip": "VIP Customer", "addNewCustomer": "Add New Customer", "addCustomer": "Add Customer", "preferredPayment": "Preferred Payment", "vipStatus": "VIP Status", "regularCustomer": "Regular Customer", "enterCustomerName": "Enter customer name", "enterPhoneNumber": "Enter phone number", "enterEmailAddress": "Enter email address", "enterCustomerAddress": "Enter customer address", "additionalNotesAboutCustomer": "Additional notes about the customer"}, "team": {"title": "Team", "newMember": "New Member", "memberDetails": "Member Details", "employeeId": "Employee ID", "fullName": "Full Name", "department": "Department", "role": "Role", "joinDate": "Join Date", "phone": "Phone", "totalMembers": "Total Members", "expandYour": "Expand Your Team", "addTalented": "Add talented individuals to strengthen your workforce", "teamManagement": "Team Management", "roleAssignment": "Role Assignment", "activeMembers": "Active Members", "inactiveMembers": "Inactive Members", "member": "Member", "status": "Status", "contact": "Contact", "location": "Location", "actions": "Actions", "active": "Active", "inactive": "Inactive", "noTeamMembers": "No team members found", "tryAdjusting": "Try adjusting your search terms.", "noTeamAvailable": "No team members available.", "joined": "Joined", "searchMembers": "Search members...", "departments": {"sales": "Sales", "delivery": "Delivery", "purchasing": "Purchasing", "administration": "Administration", "warehouse": "Warehouse"}, "roles": {"manager": "Manager", "supervisor": "Supervisor", "agent": "Agent", "driver": "Driver", "coordinator": "Coordinator", "sales": "Sales", "purchasing": "Purchasing", "delivery": "Delivery", "management": "Management", "support": "Support"}}, "fleet": {"title": "Fleet", "newTruck": "New Truck", "addTruck": "Add Truck", "addDriver": "Add Driver", "truckDetails": "Truck Details", "truckId": "Truck ID", "plateNumber": "Plate Number", "model": "Model", "capacity": "Capacity", "driver": "Driver", "totalTrucks": "Total Trucks", "trucks": "Trucks", "drivers": "Drivers", "expandYour": "Expand Your Fleet", "addTrucksDrivers": "Add trucks and drivers to strengthen your delivery capacity", "vehicleManagement": "Vehicle Management", "driverAssignment": "Driver Assignment", "activeTrucks": "Active Trucks", "totalDrivers": "Total Drivers", "availableDrivers": "Available Drivers", "inMaintenance": "In Maintenance", "truckDetailsHeader": "Truck Details", "statusLabel": "Status", "nextLicense": "Next License", "actions": "Actions", "clickRowToEdit": "Click row to edit", "loadingFleetData": "Loading fleet data...", "active": "Active", "maintenance": "Maintenance", "inactive": "Inactive", "available": "Available", "onDuty": "On Duty", "offDuty": "Off Duty", "selectDriver": "Select Driver", "selectTruck": "Select Truck", "chooseDriver": "Choose a driver...", "chooseTruck": "Choose a truck...", "truckDetailsTable": "Truck Details", "driverDetailsTable": "Driver Details", "action": "Action", "contact": "Contact", "assignedTruck": "Assigned Truck", "notAssigned": "Not assigned", "noTrucksFound": "No trucks found", "addFirstTruck": "Add your first truck to get started", "noDriversFound": "No drivers found", "addFirstDriver": "Add your first driver to get started", "next": "Next", "last": "Last", "yearsExp": "years exp.", "joined": "Joined", "license": "License", "addNewTruck": "Add New Truck", "licensePlate": "License Plate", "year": "Year", "fuelType": "Fuel Type", "mileage": "Mileage (km)", "lastMaintenance": "Last Maintenance", "nextMaintenance": "Next Maintenance", "addNewDriver": "Add New Driver", "fullName": "Full Name", "licenseNumber": "License Number", "phoneNumber": "Phone Number", "emailAddress": "Email Address", "experience": "Experience (years)", "joinDate": "Join Date", "licenseExpiry": "License Expiry", "status": {"available": "Available", "inUse": "In Use", "maintenance": "Maintenance"}, "fuelTypes": {"diesel": "Diesel", "gasoline": "Gasoline", "electric": "Electric"}}, "reports": {"title": "Reports & Analytics", "generateReport": "Generate Report", "insights": "Insights", "totalReports": "Total Reports", "reportType": "Report Type", "dateRange": "Date Range", "from": "From", "to": "To", "download": "Download", "readyToGenerate": "Ready to Generate Reports?", "createComprehensive": "Create comprehensive reports to analyze your business performance", "analytics": "Analytics", "performanceTracking": "Performance Tracking", "orderReports": "Order Reports", "customerReports": "Customer Reports", "revenueReports": "Revenue Reports", "deliveryReports": "Delivery Reports", "allReports": "All Reports", "allTypes": "All Types", "allPeriods": "All Periods", "report": "Report", "type": "Type", "period": "Period", "records": "Records", "totalValue": "Total Value", "generated": "Generated", "actions": "Actions", "ordersSummaryReport": "Orders Summary Report", "customerAnalyticsReport": "Customer Analytics Report", "revenueAnalysisReport": "Revenue Analysis Report", "deliveryPerformanceReport": "Delivery Performance Report", "searchReports": "Search reports...", "noReportsFound": "No Reports Found", "generateFirstReport": "Generate your first report to get started", "tryAdjustingSearch": "Try adjusting your search terms", "generateNewReport": "Generate New Report", "reportTitle": "Report Title", "timePeriod": "Time Period", "reportOptions": "Report Options", "includeChartsAndVisualizations": "Include Charts and Visualizations", "includeDetailedDataTables": "Include Detailed Data Tables", "enterReportTitle": "Enter report title", "ordersReport": "Orders Report", "last30Days": "Last 30 Days", "last7Days": "Last 7 Days", "last3Months": "Last 3 Months", "last6Months": "Last 6 Months", "lastYear": "Last Year", "types": {"orders": "Orders", "deliveries": "Delivery", "customers": "Customers", "revenue": "Revenue", "performance": "Performance"}, "customReportBuilder": "Custom Report Builder", "createAndManageReports": "Create and manage custom reports with charts and tables from your data", "multipleChartTypes": "Multiple Chart Types", "realTimeData": "Real-time Data", "exportOptions": "Export Options", "createReport": "Create Report", "dataSources": "Data Sources", "chartReports": "Chart Reports", "tableReports": "Table Reports", "recentRuns": "Recent Runs", "customReports": "Custom Reports", "noReportsMatch": "No reports match your search criteria.", "createFirstReport": "Create your first custom report to get started.", "createYourFirstReport": "Create Your First Report", "editCustomReport": "Edit Custom Report", "createCustomReport": "Create Custom Report", "reportName": "Report Name", "enterReportName": "Enter report name", "dataSource": "Data Source", "chartType": "Chart Type", "groupBy": "Group By", "noGrouping": "No Grouping", "aggregation": "Aggregation", "count": "Count", "sum": "Sum", "average": "Average", "minimum": "Minimum", "maximum": "Maximum", "field": "Field", "selectField": "Select Field", "updateReport": "Update Report", "runReport": "Run Report", "generatingReport": "Generating report...", "groupedBy": "Grouped by", "lastRun": "Last run", "dataTable": "Data Table", "barChart": "Bar Chart", "lineChart": "Line Chart", "pieChart": "Pie Chart", "metricCard": "Metric Card", "teamMembers": "Team Members", "fleet": "Fleet", "deliveries": "Deliveries", "allTime": "All Time", "export": "Export", "chartVisualization": "Chart visualization would be displayed here", "dataPointsFor": "data points for", "chart": "chart", "deleteReportConfig": "Are you sure you want to delete this report configuration?", "deleteReport": "Delete Report", "deleteReportConfirmation": "Are you sure you want to delete the report '{reportName}'? This action cannot be undone.", "reportData": "Report Data", "value": "Value", "averageValue": "Average Value", "fields": {"orderId": "Order ID", "customerName": "Customer Name", "orderType": "Order Type", "status": "Status", "totalAmount": "Total Amount", "createdAt": "Created Date", "deliveryAgent": "Delivery Agent", "name": "Name", "email": "Email", "phone": "Phone", "isVIP": "VIP Status", "totalOrders": "Total Orders", "totalSpent": "Total Spent", "joinDate": "Join Date", "role": "Role", "plateNumber": "Plate Number", "model": "Model", "driver": "Driver", "capacity": "Capacity", "deliveryTime": "Delivery Time", "ordersHandled": "Orders Handled", "deliveriesCompleted": "Deliveries Completed", "eta": "ETA", "priority": "Priority"}}, "settings": {"title": "Settings", "profile": "Profile", "notifications": "Notifications", "security": "Security", "appearance": "Appearance & Localization", "theme": "Theme", "language": "Language", "dateFormat": "Date Format", "timeFormat": "Time Format", "currency": "<PERSON><PERSON><PERSON><PERSON>", "themes": {"light": "Light", "dark": "Dark", "system": "System"}, "languages": {"en": "English", "ar": "العربية (Arabic)"}, "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "orderUpdates": "Order Updates", "systemAlerts": "System Alerts", "weeklyReports": "Weekly Reports", "twoFactorAuth": "Two-Factor Authentication", "sessionTimeout": "Session Timeout", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password"}, "confirmations": {"deactivateTeamMember": {"title": "Deactivate Team Member", "message": "Are you sure you want to deactivate this team member? This will preserve all historical data while preventing them from being assigned to new orders."}, "deactivateTruck": {"title": "Deactivate Truck", "message": "Are you sure you want to deactivate this truck? This will preserve all historical data while preventing it from being assigned to new deliveries."}, "deactivateDriver": {"title": "Deactivate Driver", "message": "Are you sure you want to deactivate this driver? This will preserve all historical data while preventing them from being assigned to new deliveries."}, "deactivateCustomer": {"title": "Deactivate Customer", "message": "Are you sure you want to deactivate this customer? This will preserve all order history while preventing new orders from being created."}, "deleteTeamMember": {"title": "Deactivate Team Member", "message": "Are you sure you want to deactivate this team member? This will preserve all historical data while preventing them from being assigned to new orders."}, "deleteTruck": {"title": "Deactivate Truck", "message": "Are you sure you want to deactivate this truck? This will preserve all historical data while preventing it from being assigned to new deliveries."}, "deleteDriver": {"title": "Deactivate Driver", "message": "Are you sure you want to deactivate this driver? This will preserve all historical data while preventing them from being assigned to new deliveries."}, "deleteCustomer": {"title": "Deactivate Customer", "message": "Are you sure you want to deactivate this customer? This will preserve all order history while preventing new orders from being created."}}, "toasts": {"teamMemberDeactivated": "Team member deactivated successfully", "truckDeactivated": "Truck deactivated successfully", "driverDeactivated": "Driver deactivated successfully", "customerDeactivated": "Customer deactivated successfully", "teamMemberDeleted": "Team member deactivated successfully", "truckDeleted": "Truck deactivated successfully", "driverDeleted": "Driver deactivated successfully", "customerDeleted": "Customer deactivated successfully", "operationFailed": "Operation failed. Please try again.", "saveSuccess": "Changes saved successfully", "updateSuccess": "Updated successfully"}}