import type { NextApiRequest, NextApiResponse } from 'next';
import { createSimpleShopifyClient } from '../../lib/shopify';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'PUT') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { orderId } = req.query;
  const { reason, notes } = req.body;

  if (!orderId || !reason) {
    return res.status(400).json({ error: 'Order ID and cancellation reason are required' });
  }

  try {
    const client = createSimpleShopifyClient();

    // Map our cancellation reasons to Shopify's format
    const shopifyReasonMap: { [key: string]: string } = {
      'customer': 'customer',
      'inventory': 'inventory',
      'fraud': 'fraud',
      'declined': 'declined',
      'staff': 'staff',
      'other': 'other'
    };

    const shopifyReason = shopifyReasonMap[reason] || 'other';

    // Try to cancel the order in Shopify
    let cancelResponse = null;
    let shopifyError = null;

    try {
      // First, try to get the order to verify it exists
      const orderData = await client.get(`orders/${orderId}`);
      console.log('Order found, attempting to cancel...');

      // Try to cancel the order
      cancelResponse = await client.post(`orders/${orderId}/cancel`, {
        reason: shopifyReason,
        email: true,
        refund: false
      });

      console.log('Order cancelled successfully in Shopify');
    } catch (shopifyErr: any) {
      shopifyError = shopifyErr;
      console.warn('Shopify API error:', shopifyErr.message);

      // If the order doesn't exist in Shopify or can't be cancelled,
      // we'll still mark it as cancelled locally
      if (shopifyErr.message.includes('404') || shopifyErr.message.includes('Not Found')) {
        console.log('Order not found in Shopify, marking as cancelled locally');
      } else if (shopifyErr.message.includes('422') || shopifyErr.message.includes('Unprocessable')) {
        console.log('Order cannot be cancelled in Shopify (might already be cancelled), marking as cancelled locally');
      } else {
        console.log('Other Shopify error, marking as cancelled locally');
      }
    }

    // Try to add notes to the order (if it exists)
    try {
      const noteText = notes ? `Cancelled: ${reason}. Additional notes: ${notes}` : `Cancelled: ${reason}`;
      await client.put(`orders/${orderId}`, {
        order: {
          id: orderId,
          note: noteText,
          tags: `cancelled,reason-${reason}`
        }
      });
      console.log('Order notes updated successfully');
    } catch (noteError) {
      console.warn('Could not update order notes:', noteError);
      // This is not critical, so we continue
    }

    // Return success response
    res.status(200).json({
      success: true,
      message: `Order ${orderId} cancelled successfully`,
      data: {
        orderId,
        reason,
        notes,
        shopifyResponse: cancelResponse,
        shopifyError: shopifyError ? shopifyError.message : null,
        cancelledAt: new Date().toISOString()
      }
    });

  } catch (error: any) {
    console.error('Error cancelling order:', error);
    res.status(500).json({
      error: 'Failed to cancel order',
      message: error.message
    });
  }
}
