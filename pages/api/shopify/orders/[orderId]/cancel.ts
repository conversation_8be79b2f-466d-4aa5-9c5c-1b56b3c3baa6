import type { NextApiRequest, NextApiResponse } from 'next';
import { createSimpleShopifyClient } from '../../lib/shopify';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'PUT') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { orderId } = req.query;
  const { reason, notes } = req.body;

  if (!orderId || !reason) {
    return res.status(400).json({ error: 'Order ID and cancellation reason are required' });
  }

  try {
    const client = createSimpleShopifyClient();
    
    // Map our cancellation reasons to Shopify's format
    const shopifyReasonMap: { [key: string]: string } = {
      'customer': 'customer',
      'inventory': 'inventory',
      'fraud': 'fraud', 
      'declined': 'declined',
      'other': 'other'
    };

    const shopifyReason = shopifyReasonMap[reason] || 'other';
    
    // Cancel the order in Shopify
    const cancelResponse = await client.post(`orders/${orderId}/cancel`, {
      reason: shopifyReason,
      email: true, // Send cancellation email to customer
      refund: false // Don't automatically refund - handle separately if needed
    });

    // Add additional notes if provided
    if (notes) {
      await client.put(`orders/${orderId}`, {
        order: {
          id: orderId,
          note: `Cancelled: ${reason}. Additional notes: ${notes}`,
          tags: `cancelled,reason-${reason}`
        }
      });
    } else {
      await client.put(`orders/${orderId}`, {
        order: {
          id: orderId,
          note: `Cancelled: ${reason}`,
          tags: `cancelled,reason-${reason}`
        }
      });
    }

    res.status(200).json({
      success: true,
      message: `Order ${orderId} cancelled successfully`,
      data: cancelResponse
    });

  } catch (error: any) {
    console.error('Error cancelling order:', error);
    res.status(500).json({
      error: 'Failed to cancel order',
      message: error.message
    });
  }
}
