import { shopifyApi, ApiVersion, LATEST_API_VERSION } from '@shopify/shopify-api';

// Shopify API configuration
export const shopify = shopifyApi({
  apiKey: process.env.SHOPIFY_API_KEY!,
  apiSecretKey: process.env.SHOPIFY_SECRET_KEY!,
  scopes: ['read_orders', 'read_products', 'read_customers'],
  hostName: process.env.SHOPIFY_APP_URL || 'localhost:3000',
  apiVersion: LATEST_API_VERSION,
  isEmbeddedApp: false,
  // For private apps, we don't need OAuth
  isPrivateApp: true,
});

// Shopify store configuration
export const SHOPIFY_CONFIG = {
  storeDomain: process.env.SHOPIFY_STORE_DOMAIN!,
  accessToken: process.env.SHOPIFY_ACCESS_TOKEN!,
  apiVersion: '2025-07' as ApiVersion,
};

// Helper function to create REST client
export function createShopifyRestClient() {
  return new shopify.clients.Rest({
    session: {
      shop: SHOPIFY_CONFIG.storeDomain,
      accessToken: SHOPIFY_CONFIG.accessToken,
    },
  });
}

// Helper function to create GraphQL client
export function createShopifyGraphQLClient() {
  return new shopify.clients.Graphql({
    session: {
      shop: SHOPIFY_CONFIG.storeDomain,
      accessToken: SHOPIFY_CONFIG.accessToken,
    },
  });
}
